<template>
  <div style="margin: 24px 2% 0 2%;">
    <a-row :gutter="16">
      <a-col
        :span="5"
        class="select-col"
      >
        <div class="input">
          <a-range-picker
            v-model:value="trendDateRange"
            :allow-clear="false"
            :disabled-date="(current) => current && current > dayjs().endOf('day')"
            style="width: 260px"
          />
        </div>
      </a-col>
      <a-col
        :span="5"
        class="select-col"
      >
        <div class="input">
          <a-select
            v-model:value="value1"
            mode="multiple"
            allowClear
            style="width: 200px"
            placeholder="请选择平台"
            :max-tag-count="1"
            :max-tag-placeholder="maxTagPlaceholder"
            @change="handleChange"
          >
            <a-select-option value="all">选择全部</a-select-option>
            <a-select-option
              v-for="platform in options1"
              :key="platform.value"
              :value="platform.value"
            >
              {{ platform.label }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col
        :span="5"
        class="select-col"
      >
        <div class="input">
          <a-select
            v-model:value="value2"
            mode="multiple"
            allowClear
            style="width: 200px"
            placeholder="请选择国家"
            :max-tag-count="1"
            :max-tag-placeholder="maxTagPlaceholder"
            @change="handleChange"
          >
            <a-select-option value="all">选择全部</a-select-option>
            <a-select-option
              v-for="country in options2"
              :key="country.value"
              :value="country.value"
            >
              {{ country.label }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col
        :span="5"
        class="select-col"
      >
        <div class="input">
          <a-select
            v-model:value="selectedGenre"
            mode="multiple"
            allowClear
            placeholder="选择游戏类别"
            style="width: 200px"
            :max-tag-count="1"
            :max-tag-placeholder="maxTagPlaceholder"
          >
            <a-select-option
              value="all"
              @click="selectAllGenres"
            >选择全部</a-select-option>
            <a-select-option
              v-for="genre in games"
              :key="genre.value"
              :value="genre.value"
            >
              {{ genre.label }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col
        :span="4"
        class="select-col"
      >
        <div class="input">
          <a-button
            type="primary"
            @click="handleSearch"
          >
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
        </div>
      </a-col>
    </a-row>
  </div>
  <div class="TopBar">
    <span style="border-bottom: 3px solid rgba(8, 147, 207, 1);font-size: 18px;margin-left: 15px;font-weight: 500;">我的发行商</span>
    <div>
      <a-row>
        <a-col
          v-for="(publisher, idx) in myPublishers"
          :key="publisher.name"
          :span="2"
          style="top: 15px;"
          class="logo-col"
        >
          <div style="position: relative; display: inline-block;">
            <img
              style="margin-top: 10px; margin-left: 15px; cursor: pointer;"
              :src="publisher.logo || '/src/assets/images/game1.png'"
              @click="toPublisherDetail(publisher)"
            />
            <span
              style="display: flex;margin-left: 10px;font-size: 12px;"
              :title="publisher.name"
            >
              {{ publisher.name.length > 8 ? publisher.name.slice(0, 8) + '...' : publisher.name }}
            </span>
            <!-- 小角标 -->
            <!-- <span
      style="
        position: absolute;
        top: 10px;
        right: 0px;
        width: 18px;
        height: 18px;
        background: #ff4d4f;
        color: #fff;
        border-radius: 50%;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 2;
      "
      title="取消关注"
      @click.stop="unFocusPublisher(publisher.name)"
    >×</span> -->
            <button
              class="delete-button"
              @click="unFocusPublisher(publisher.name)"
            >-</button>
          </div>
          <!-- <span style="display: flex;margin-left: 10px;">{{ publisher.name }}</span> -->
        </a-col>
        <a-col
          :span="2"
          style="top: 15px;"
          class="logo-col"
        >
          <a-button
            @click="showModal"
            style="height: 50px;width: 50px;margin-top: 10px;"
            :icon="h(PlusOutlined)"
          ></a-button>
          <a-modal
            v-model:open="open"
            title="添加发行商"
            @ok="handleOk"
            @cancel="handleCancel"
          >
            <a-select
              v-model:value="selectedPublisher"
              :options="publisherOptions"
              style="width: 100%;"
              placeholder="请选择发行商"
              :loading="publisherLoading"
              :show-search="true"
              :filter-option="false"
              @search="onPublisherSearch"
              @popup-scroll="onPublisherScroll"
              label-in-value
            >
              <template #option="{ label }">
                <span>{{ label }}</span>
              </template>
            </a-select>
            <p style="margin-left: 20px;margin-top: 20px;">如未找到可搜索发行商</p>
          </a-modal>
        </a-col>
      </a-row>
    </div>
  </div>
  <div class="MBar">
    <div style="border-bottom: 1px solid rgba(196, 200, 204, 0.5);">
      <a-row>
        <a-col
          :span="1"
          style="top: 10px;"
        >
          <img
            style=" margin-left: 15px;height: 60px;width: 60px;margin-bottom: 10px;"
            src="/src/assets/images/game1.png"
          />
        </a-col>
        <a-col
          :span="21"
          style="top: 10px;left: 24px;"
        >
          <span style="font-size: 18px;font-weight: 500;">{{ publisherTableData[0]?.name || '-' }}</span>
          <span style="display: flex;color: rgba(147, 150, 153, 1);margin-top: 2px;">游戏发行</span>
        </a-col>
        <a-col
          :span="2"
          style="top: 10px;"
        >
          <a-button
            style="margin-top: 10px;"
            class="btn"
            @click="toXiangqing(publisherTableData[0])"
          >详情</a-button>
        </a-col>
      </a-row>
    </div>
    <div
      id="ranking"
      class="chart"
    >
      <template v-if="!data.length">
        <div class="empty-data-container">
          <i class="empty-icon">🔍</i>
          <p>没有找到匹配的数据</p>
          <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
        </div>
      </template>
    </div>
  </div>
  <div class="BBar">
    <a-table
      :columns="columns"
      :data-source="publisherTableData"
      :pagination="false"
      :loading="loading"
      :row-key="record => record.id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <span>{{ record.name }}</span>
        </template>
        <template v-else-if="column.key === 'downloadTimePer'">
          <div style="display: flex; align-items: center; gap: 8px;">
            <a-progress
              :percent="Number((record.downloadTimePer * 100).toFixed(2))"
              size="small"
              :show-info="false"
              stroke-color="#40a9ff"
              style="flex: 1; min-width: 0;"
            />
            <span style="min-width: 48px; text-align: right; font-weight: bold; color: #40a9ff;">
              {{ Number((record.downloadTimePer * 100).toFixed(2)) }}%
            </span>
          </div>
        </template>
<template v-else-if="column.key === 'incomeSumPer'">
  <div style="display: flex; align-items: center; gap: 8px;">
    <a-progress
      :percent="Number((record.incomeSumPer * 100).toFixed(2))"
      size="small"
      :show-info="false"
      stroke-color="#ff9c6e"
      style="flex: 1; min-width: 0;"
    />
    <span style="min-width: 48px; text-align: right; font-weight: bold; color: #ff9c6e;">
      {{ Number((record.incomeSumPer * 100).toFixed(2)) }}%
    </span>
  </div>
</template>
        </template>
        <template #emptyText>
          <div class="empty-data-container">
            <i class="empty-icon">🔍</i>
            <p>没有找到匹配的数据</p>
            <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
          </div>
        </template>
      </a-table>
    </div>
  </template>
  
  <script lang="ts" setup>
import { ref, h, onMounted, onUnmounted } from 'vue';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import * as echarts from 'echarts';
import { useListPage } from '/@/hooks/system/useListPage';
import { ActionItem, BasicColumn, BasicTable, TableAction } from '/@/components/Table';
import { useRouter } from 'vue-router';
import {
  focusPublisherApi,
  unfocusPublisherApi,
  getMyPublishersApi,
  getAllPublishersApi,
  getAllGameGenresApi,
  getPublisherRankApi,
  getAllCountryApi,
  getAllDeviceApi,
} from '@/api/store-information/my-publisher/index';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const router = useRouter();
const toXiangqing = (publisher) => {
  if (!publisher?.name) {
    message.warning('暂无发行商信息');
    return;
  }
  router.push({
    name: 'publisherDetails',
    query: {
      id: publisher.publisherId || publisher.id || '', // publisherId
      name: publisher.name, // publisherName
    },
  });
};

const open = ref<boolean>(false);
const showModal = () => {
  open.value = true;
  publisherPage.value = 1;
  publisherSearch.value = '';
  publisherOptions.value = [];
  fetchAllPublishers();
};
const handleOk = async () => {
  if (!selectedPublisher.value) {
    message.warning('请选择要关注的发行商');
    return;
  }
  try {
    const res = await focusPublisherApi(selectedPublisher.value.label);
    if (res === '已经关注啦') {
      message.info('该发行商已关注');
      return; // 不关闭弹窗
    }
    if (res === '超过关注上限') {
      message.info('超过关注上限');
      return; // 不关闭弹窗
    }
    message.success('关注成功');
    open.value = false;
    selectedPublisher.value = undefined;
    fetchMyPublishers(); // 刷新
  } catch (e) {
    message.error('关注失败');
  }
};
const handleCancel = () => {
  open.value = false;
  selectedPublisher.value = undefined;
};
const value = ref<string>('');

// 下拉分页查所有发行商
const publisherOptions = ref<{ label: string; value: string; logo: string }[]>([]);
const publisherPage = ref(1);
const publisherPageSize = 10;
const publisherTotal = ref(0);
const publisherSearch = ref('');
const publisherLoading = ref(false);
const selectedPublisher = ref<{ label: string; value: string } | undefined>(undefined);

const fetchAllPublishers = async (append = false) => {
  publisherLoading.value = true;
  try {
    const res = await getAllPublishersApi(publisherPage.value, publisherPageSize, publisherSearch.value);
    const list = res?.records || [];
    publisherTotal.value = res?.total || 0;
    const newOptions = list.map((item: any) => ({
      label: item.publisher,
      value: item.publisher,
      logo: item.logo,
    }));
    if (append) {
      publisherOptions.value = [...publisherOptions.value, ...newOptions];
    } else {
      publisherOptions.value = newOptions;
    }
  } catch (e) {
    message.error('获取发行商列表失败');
  } finally {
    publisherLoading.value = false;
  }
};
const onPublisherSearch = (val: string) => {
  publisherSearch.value = val;
  publisherPage.value = 1;
  fetchAllPublishers();
};
const toPublisherDetail = (publisher: any) => {
  router.push({
    name: 'publisherDetails',
    query: {
      id: publisher.publisherId || publisher.id || '', // publisherId
      name: publisher.name || publisher.publisherName || '', // publisherName
    },
  });
};

const unFocusPublisher = async (publisherName: string) => {
  try {
    await unfocusPublisherApi(publisherName);
    message.success('已取消关注');
    fetchMyPublishers();
  } catch (e) {
    message.error('取消关注失败');
  }
};
const onPublisherScroll = (e: Event) => {
  const target = e.target as HTMLElement;
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
    if (publisherOptions.value.length < publisherTotal.value && !publisherLoading.value) {
      publisherPage.value += 1;
      fetchAllPublishers(true);
    }
  }
};
// 我的发行商数据
const myPublishers = ref<{ name: string; logo: string }[]>([]);
const publisherTableData = ref<any[]>([]);

const fetchMyPublishers = async () => {
  try {
    const res = await getMyPublishersApi();
    console.log('获取我的发行商数据:', res);
    // 正确取 records 数组
    myPublishers.value = Array.isArray(res)
      ? res.map((item) => ({
          name: item.name,
          logo: item.logo,
        }))
      : [];
    publisherTableData.value = Array.isArray(res) ? res : [];
    await drawRankChart(); // 加载完表格后绘制排名折线图
  } catch (e) {
    myPublishers.value = [];
    publisherTableData.value = [];
  }
};

onMounted(() => {
  fetchMyPublishers();
  fetchGameGenres();

  // onUnmounted(() => {
  //   myChart?.dispose();
  // });
});
const options1 = ref<{ value: string; label: string }[]>([]);
const fetchPlatforms = async () => {
  try {
    const res = await getAllDeviceApi();
    // 格式化平台数据，确保与之前一致
    options1.value = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value === 'apple' ? 'App Store' : item.value === 'google' ? 'Google Play' : item.value,
    }));
  } catch (e) {
    options1.value = [];
  }
};
const handleChange = (value) => {
  if (value.includes('all')) {
    // 如果选择了"选择全部"，则选中所有选项
    if (value1.value.includes('all')) {
      value1.value = options1.value.map((item) => item.value);
    }
    if (value2.value.includes('all')) {
      value2.value = options2.value.map((item) => item.value);
    }
    if (selectedGenre.value.includes('all')) {
      selectedGenre.value = games.value.map((item) => item.value);
    }
  }
  console.log('Selected values:', value);
};
const value1 = ref<string[]>([]);

const options2 = ref<{ value: string; label: string }[]>([]);
const value2 = ref<string[]>([]);
const games = ref<{ value: string; label: string }[]>([]);
const fetchGameGenres = async () => {
  try {
    const res = await getAllGameGenresApi();
    games.value = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value,
    }));
  } catch (e) {
    games.value = [];
  }
};
const options3 = ref([
  {
    value: 'kapai',
    label: '卡牌',
  },
  {
    value: 'celve',
    label: '策略',
  },
  {
    value: 'xiuxian',
    label: '休闲',
  },
]);
const value3 = ref({
  value: 'category',
  label: '请选择游戏类别',
});

const columns: BasicColumn[] = [
  {
    title: '发行商',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '下载量',
    dataIndex: 'downloadTimeSum',
    key: 'downloadTimeSum',
  },
  {
    title: '占总下载量的%',
    dataIndex: 'downloadTimePer',
    key: 'downloadTimePer',
    customRender: ({ record }) =>
      h('a-progress', {
        percent: Number(record.downloadTimePer * 100),
        size: 'small',
      }),
  },
  {
    title: '净收入',
    dataIndex: 'incomeSum',
    key: 'incomeSum',
  },
  {
    title: '占总净收入的%',
    dataIndex: 'incomeSumPer',
    key: 'incomeSumPer',
    customRender: ({ record }) =>
      h('a-progress', {
        percent: Number((record.incomeSumPer * 100).toFixed(2)),
        size: 'small',
      }),
  },
];

const { tableContext: tableContext1 } = useListPage({
  tableProps: {
    dataSource: publisherTableData,
    columns: columns,
    size: 'small',
    actionColumn: {
      width: 120,
    },
    showActionColumn: false,
    striped: true,
    showTableSetting: false,
    pagination: false,
  },
});
const [registerTable] = tableContext1;
// 获取国家数据
const fetchCountries = async () => {
  try {
    const res = await getAllCountryApi();

    if (res && Array.isArray(res)) {
      options2.value = res
        .filter((item: any) => {
          return (
            item &&
            typeof item === 'object' &&
            item.value &&
            typeof item.value === 'string' &&
            item.value.trim() !== '' &&
            item.value !== '5' &&
            item.value !== '6'
          ); // 过滤掉数字ID
        })
        .map((item: any) => ({
          value: item.value.trim(),
          label: item.value.trim(),
        }));
    } else {
      throw new Error('API返回数据格式不正确');
    }
  } catch (e) {
    console.error('获取国家数据失败:', e);
    // 如果API失败，使用默认数据
    options2.value = [
      { value: '菲律宾', label: '菲律宾' },
      { value: '柬埔寨', label: '柬埔寨' },
      { value: '马来西亚', label: '马来西亚' },
      { value: '泰国', label: '泰国' },
      { value: '文莱', label: '文莱' },
      { value: '新加坡', label: '新加坡' },
      { value: '印度尼西亚', label: '印度尼西亚' },
      { value: '越南', label: '越南' },
      { value: '缅甸', label: '缅甸' },
      { value: '中国台湾', label: '中国台湾' },
      { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
    ];
  }
};
const drawRankChart = async () => {
  if (!publisherTableData.value.length) return;
  const topPublisher = publisherTableData.value[0].name;
  // 请求排名数据
  const res = await getPublisherRankApi(topPublisher, '马来西亚');
  const data = res?.records || [];
  if (!data.length) return;

  // 处理数据
  // 1. 获取所有分类
  const categories = [...new Set(data.map((item) => item.category_name))];
  // 2. 按 app_name 分组
  const appMap = {};
  data.forEach((item) => {
    if (!appMap[item.app_name]) appMap[item.app_name] = {};
    appMap[item.app_name][item.category_name] = item.ranking;
  });

  // 3. 组装 series
  const series = Object.keys(appMap).map((appName) => ({
    name: appName,
    type: 'line',
    data: categories.map((cat) => appMap[appName][cat] ?? null),
    connectNulls: true,
  }));

  // 4. 绘制图表
  let myChart = echarts.init(document.getElementById('ranking'));
  myChart.setOption({
    title: { text: '各游戏分类排名', left: 'center' },
    tooltip: { trigger: 'axis' },
    legend: { data: Object.keys(appMap), top: 30 },
    xAxis: { type: 'category', data: categories, name: '分类' },
    yAxis: { type: 'value', name: '排名', inverse: true },
    series,
  });
};

interface GameType {
  value: string;
  label: string;
}

const selectedGenre = ref<string[]>([]);
const trendDateRange = ref<[dayjs.Dayjs, dayjs.Dayjs]>([dayjs().subtract(7, 'day'), dayjs()]);

const genreOptions = ref<{ value: string; label: string }[]>([]);

// 获取游戏类型列表
const fetchGameTypes = async () => {
  try {
    const response = await getAllGameGenresApi();
    if (response && Array.isArray(response)) {
      genreOptions.value = [
        ...response.map((item: any) => ({
          value: item.value || '',
          label: item.label || '',
        })),
      ];
    }
  } catch (error) {
    console.error('获取游戏类型失败:', error);
  }
};

// 在组件挂载时获取游戏类型
onMounted(() => {
  fetchGameTypes();
  fetchMyPublishers();
  fetchGameGenres();
  fetchCountries();
  fetchPlatforms();
});

// 查询方法
const handleSearch = async () => {
  try {
    const params = {
      startDate: trendDateRange.value[0].format('YYYY-MM-DD'),
      endDate: trendDateRange.value[1].format('YYYY-MM-DD'),
      platform: value1.value,
      country: value2.value,
      genre: selectedGenre.value,
    };

    const response = await getMyPublishersApi(params);

    if (response) {
      console.log('查询结果:', response);
      // TODO: 根据实际需求处理响应数据
    }
  } catch (error) {
    console.error('查询失败:', error);
    message.error('查询失败');
  }
};

const loading = ref(false);
const data = ref<any[]>([]);

const handleTableChange = (pagination, filters, sorter) => {
  console.log('Table change:', pagination, filters, sorter);
};

const handleAdd = () => {
  // 添加发行商的逻辑
};

const handleEdit = (record) => {
  // 编辑发行商的逻辑
};

const fetchData = async () => {
  loading.value = true;
  try {
    const response = await getMyPublishersApi();
    data.value = response.data || [];
  } catch (error) {
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchData();
});

const maxTagPlaceholder = (omittedValues) => `+${omittedValues.length}...`;

const selectAllPlatforms = () => {
  value1.value = options1.value.map((item) => item.value);
};

const selectAllCountries = () => {
  value2.value = options2.value.map((item) => item.value);
};

const selectAllGenres = () => {
  selectedGenre.value = games.value.map((item) => item.value);
};
</script>
  
  <style lang="less" scoped>
.input {
  display: flex;
  align-items: center;

  .ant-btn {
    margin-left: -8px;
  }
}

.select-col {
  // margin-bottom: 8px;\

  margin-left: -20px;
}
.TopBar {
  border-radius: 8px;
  margin: 24px 2% 0 10px;
  height: 160px;
  background: #fff;
  padding: 10px;
  box-shadow: 0px 0px 0px rgba(77, 85, 117, 0.05), 0px 3px 7px rgba(77, 85, 117, 0.05), 0px 5px 14px rgba(77, 85, 117, 0.04),
    0px 13px 18px rgba(77, 85, 117, 0.03), 0px 20px 20px rgba(77, 85, 117, 0.01), 0px 35px 30px rgba(77, 85, 117, 0);
}
.logo-col img {
  width: 50px;
  height: 50px;
}
.MBar {
  border-radius: 8px;
  margin: 24px 2% 0 10px;
  background: #fff;
  padding: 10px;
  box-shadow: 0px 0px 0px rgba(77, 85, 117, 0.05), 0px 3px 7px rgba(77, 85, 117, 0.05), 0px 5px 14px rgba(77, 85, 117, 0.04),
    0px 13px 18px rgba(77, 85, 117, 0.03), 0px 20px 20px rgba(77, 85, 117, 0.01), 0px 35px 30px rgba(77, 85, 117, 0);
}
.btn {
  background: rgba(41, 173, 230, 1);
  color: white;
  width: 88px;
  height: 32px;
}
.BBar {
  margin: 24px 2% 0 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.chart {
  height: 400px;
  position: relative;

  .empty-data-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #999;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 8px 0;
      font-size: 14px;

      &.empty-data-tip {
        color: #666;
        font-size: 12px;
      }
    }
  }
}
.ant-table-wrapper .ant-table-thead > tr > th {
  background: rgba(80, 189, 235, 0.35);
}
.jeecg-basic-table-row__striped td {
  background: rgba(80, 189, 235, 0.2);
}
.delete-button {
  position: absolute;
  top: 0px;
  right: -10px;
  width: 20px;
  height: 20px;
  background-color: #ccc;
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 16px;
  cursor: pointer;
}
</style>