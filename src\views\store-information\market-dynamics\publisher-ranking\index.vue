<template>
  <div class="publisher-ranking-container">
    <!-- 头部信息卡片 -->
    <div class="header-card">
      <div class="header-content">
        <div class="top-publisher-info">
          <div class="publisher-badge">
            <div class="badge-icon">👑</div>
            <div class="badge-content">
              <h2 class="top-publisher-name">{{ topAppInfo.publisherName || '上海米哈游' }}</h2>
              <p class="publisher-subtitle">排行榜第一名发行商</p>
            </div>
          </div>

          <div class="search-section">
            <div class="search-label">
              <i class="search-icon">🔍</i>
              <span>快速查找发行商</span>
            </div>
            <a-select
              v-model:value="Search_content"
              show-search
              placeholder="输入发行商名称搜索..."
              class="modern-search"
              :default-active-first-option="false"
              :show-arrow="true"
              :filter-option="false"
              :not-found-content="publisherLoading ? '加载中...' : '未找到相关发行商'"
              @search="onPublisherSearch"
              @select="onPublisherSelect"
              @popup-scroll="onPopupScroll"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
              <a-select-option
                v-for="option in publisherOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计数据卡片 -->
    <div class="stats-overview">
      <div class="stats-header">
        <h3 class="stats-title">
          <i class="title-icon">📊</i>
          榜首发行商数据概览
        </h3>
      </div>

      <div class="stats-grid">
        <div class="stat-card games-stat">
          <div class="stat-icon">
            <i class="icon">🎮</i>
          </div>
          <div class="stat-content">
            <div class="stat-label">发行游戏数</div>
            <div class="stat-value">{{ topAppInfo.gameNumber ?? '--' }}</div>
          </div>
        </div>

        <div class="stat-card revenue-stat">
          <div class="stat-icon">
            <i class="icon">💰</i>
          </div>
          <div class="stat-content">
            <div class="stat-label">净收入</div>
            <div class="stat-value">{{ topAppInfo.incomeSum ?? '--' }}</div>
          </div>
        </div>

        <div class="stat-card region-stat">
          <div class="stat-icon">
            <i class="icon">🌍</i>
          </div>
          <div class="stat-content">
            <div class="stat-label">热门地区</div>
            <div class="stat-value">{{ topAppInfo.region || '--' }}</div>
          </div>
        </div>

        <div class="stat-card downloads-stat">
          <div class="stat-icon">
            <i class="icon">📱</i>
          </div>
          <div class="stat-content">
            <div class="stat-label">总下载量</div>
            <div class="stat-value">{{ topAppInfo.downloadTimeSum ?? '--' }}</div>
          </div>
        </div>

        <div class="stat-card website-stat">
          <div class="stat-icon">
            <i class="icon">🌐</i>
          </div>
          <div class="stat-content">
            <div class="stat-label">官方网站</div>
            <div class="stat-value website-link">{{ topAppInfo.site || '暂无' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 排行榜表格卡片 -->
    <div class="ranking-table-card">
      <!-- 筛选区域 -->
      <div class="filter-panel">
        <div class="filter-header">
          <h3 class="filter-title">
            筛选条件
          </h3>
          <div class="filter-actions">
            <a-button
              type="text"
              size="small"
              @click="resetFilters"
              class="reset-btn"
            >
              重置筛选
            </a-button>
          </div>
        </div>

        <div class="filter-grid">
          <div class="filter-group">
            <a-range-picker
              v-model:value="value1"
              :presets="rangePresets"
              @change="onRangeChange"
              class="modern-filter-input"
              placeholder="['开始日期', '结束日期']"
            />
          </div>

          <div class="filter-group">

            <a-select
              v-model:value="country_data"
              mode="multiple"
              allowClear
              placeholder="选择国家或地区"
              class="modern-filter-input"
              :max-tag-count="2"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handleCountryChange"
            >
              <a-select-option value="all" @click="selectAllCountries">
                <span class="select-all-option">✨ 选择全部</span>
              </a-select-option>
              <a-select-option
                v-for="country in select_country"
                :key="country.value"
                :value="country.value"
              >
                {{ country.label }}
              </a-select-option>
            </a-select>
          </div>

          <div class="filter-group">

            <a-select
              v-model:value="equipment"
              mode="multiple"
              allowClear
              placeholder="选择平台"
              class="modern-filter-input"
              :max-tag-count="2"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handleEquipmentChange"
            >
              <a-select-option value="all" @click="selectAllEquipment">
                <span class="select-all-option">✨ 选择全部</span>
              </a-select-option>
              <a-select-option
                v-for="item in equipment_data"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>

          <div class="filter-group">

            <a-select
              v-model:value="selectedGameCategory"
              mode="multiple"
              allowClear
              placeholder="选择游戏类别"
              class="modern-filter-input"
              :max-tag-count="2"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handleGameCategoryChange"
            >
              <a-select-option value="all" @click="selectAllGames">
                <span class="select-all-option">✨ 选择全部</span>
              </a-select-option>
              <a-select-option
                v-for="game in games"
                :key="game.value"
                :value="game.value"
              >
                {{ game.label }}
              </a-select-option>
            </a-select>
          </div>

          <div class="filter-group">

            <a-select
              v-model:value="selectedPayment"
              mode="multiple"
              allowClear
              placeholder="选择付费情况"
              class="modern-filter-input"
              :max-tag-count="2"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handlePaymentChange"
            >
              <a-select-option value="all" @click="selectAllPayment">
                <span class="select-all-option">✨ 选择全部</span>
              </a-select-option>
              <a-select-option
                v-for="item in paymentOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>

          <div class="filter-group search-group">
            <a-button
              type="primary"
              @click="bijiao"
              :loading="loading"
              class="modern-search-btn"
              size="large"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>
          </div>
        </div>
      </div>
      <!-- 排行榜表格 -->
      <div class="table-section">
        <div class="table-header">
          <h3 class="table-title">
            <i class="title-icon">🏆</i>
            发行商排行榜
          </h3>

        </div>

        <div class="table-wrapper">
          <a-table
            :columns="columns"
            :data-source="Publisher_Date"
            :pagination="paginationProp"
            row-key="publisher"
            size="middle"
            class="modern-ranking-table"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'ranking'">
                <div class="ranking-cell">
                  <div :class="['ranking-badge', getRankingClass(record.ranking)]">
                    <span v-if="record.ranking <= 3" class="ranking-crown">
                      {{ record.ranking === 1 ? '🥇' : record.ranking === 2 ? '🥈' : '🥉' }}
                    </span>
                    <span class="ranking-number">
                      {{ record.ranking === null || record.ranking === undefined || record.ranking === '' ? '-' : record.ranking }}
                    </span>
                  </div>
                </div>
              </template>

              <template v-if="column.key === 'publisher'">
                <div class="publisher-cell">
                  <div class="publisher-info">
                    <span
                      class="publisher-name"
                      @click.stop="goToPublisherDetail(record)"
                    >
                      {{ record.publisherName }}
                    </span>
                    <div class="publisher-meta">
                      <span class="publisher-id">ID: {{ record.publisherId || 'N/A' }}</span>
                    </div>
                  </div>
                </div>
              </template>

              <template v-else-if="column.key === 'percentage_download'">
                <div class="progress-cell">
                  <div class="progress-wrapper">
                    <a-progress
                      :percent="Number((record.downloadTimePer * 100).toFixed(2))"
                      size="small"
                      :show-info="false"
                      stroke-color="linear-gradient(90deg, #667eea, #764ba2)"
                      trail-color="#f0f0f0"
                      stroke-width="6"
                      class="modern-progress"
                    />
                    <span class="progress-text download-text">
                      {{ Number((record.downloadTimePer * 100).toFixed(2)) }}%
                    </span>
                  </div>
                </div>
              </template>

              <template v-else-if="column.key === 'percentage_income'">
                <div class="progress-cell">
                  <div class="progress-wrapper">
                    <a-progress
                      :percent="Number((record.incomeSumPer * 100).toFixed(2))"
                      size="small"
                      :show-info="false"
                      stroke-color="linear-gradient(90deg, #f093fb, #f5576c)"
                      trail-color="#f0f0f0"
                      stroke-width="6"
                      class="modern-progress"
                    />
                    <span class="progress-text income-text">
                      {{ Number((record.incomeSumPer * 100).toFixed(2)) }}%
                    </span>
                  </div>
                </div>
              </template>

              <template v-else-if="column.key === 'downloadTimeSum'">
                <div class="number-cell">
                  <span class="number-value">{{ formatNumber(record.downloadTimeSum) }}</span>
                </div>
              </template>

              <template v-else-if="column.key === 'incomeSum'">
                <div class="number-cell">
                  <span class="number-value income-value">{{ formatCurrency(record.incomeSum) }}</span>
                </div>
              </template>

              <template v-else>
                <span class="default-cell">{{ record[column.dataIndex] || '-' }}</span>
              </template>
            </template>

            <!-- 空数据显示 -->
            <template #emptyText>
              <div class="empty-state">
                <div class="empty-icon">🏆</div>
                <div class="empty-title">暂无排行榜数据</div>
                <div class="empty-description">请调整筛选条件后重新查询</div>
                <a-button type="primary" @click="bijiao" class="retry-btn">
                  重新查询
                </a-button>
              </div>
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="basic-table-demo" setup>
import { ref, onMounted, h } from 'vue';
import { useRouter } from 'vue-router';
import { getPublisherInfoApi, getAllGenreApi, getAllDeviceApi, queryPublisherByNameApi, getAllCountryApi } from '@/api/store-information/market-dynamics/publisher-ranking/index';
import type { CascaderProps, SelectProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { columns } from './component/From_component.data';
import { SearchOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import * as echarts from 'echarts';

const Publisher_Date = ref<any[]>([]);
const topAppInfo = ref<any>({});
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

// 定义 RangeValue 类型
type RangeValue = [Dayjs, Dayjs];

// 设备选择的数据
const equipment_data = ref([
  {
    label: 'App Store',
    value: 'apple',
  },
  // {
  //   label: 'Google Play',
  //   value: 'google',
  // },
]);
// 选择后的设备数据
const equipment = ref<string[]>([]);
const games = ref<{ value: string; label: string }[]>([]);
const fetchGameGenres = async () => {
  try {
    const res = await getAllGenreApi();
    games.value = [
      ...(res || []).map((item: any) => ({
        value: item.value,
        label: item.value,
      })),
    ];
  } catch (e) {}
};

// 获取国家数据
const fetchCountries = async () => {
  try {
    const res = await getAllCountryApi();

    if (res && Array.isArray(res)) {
      select_country.value = res
        .filter((item: any) => {
          return item &&
                 typeof item === 'object' &&
                 item.value &&
                 typeof item.value === 'string' &&
                 item.value.trim() !== '' &&
                 item.value !== '5' &&
                 item.value !== '6'; // 过滤掉数字ID
        })
        .map((item: any) => ({
          value: item.value.trim(),
          label: item.value.trim(),
        }));
    } else {
      throw new Error('API返回数据格式不正确');
    }
  } catch (e) {
    // 如果API失败，使用默认数据
    select_country.value = [
      { value: '菲律宾', label: '菲律宾' },
      { value: '柬埔寨', label: '柬埔寨' },
      { value: '马来西亚', label: '马来西亚' },
      { value: '泰国', label: '泰国' },
      { value: '文莱', label: '文莱' },
      { value: '新加坡', label: '新加坡' },
      { value: '印度尼西亚', label: '印度尼西亚' },
      { value: '越南', label: '越南' },
      { value: '缅甸', label: '缅甸' },
      { value: '中国台湾', label: '中国台湾' },
      { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
    ];
  }
};
const options: CascaderProps['options'] = equipment_data.value;

// 时间选择
const rangePresets: { label: string; value: [Dayjs, Dayjs] }[] = [
  { label: '最近一周', value: [dayjs().add(-7, 'day'), dayjs()] },
  { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
  { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
  { label: '最近半年', value: [dayjs().add(-6, 'month'), dayjs()] },
  { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
  { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
];

// 设置默认值为近一年
const value1 = ref<RangeValue>([dayjs().add(-1, 'year'), dayjs()]);

// 国家地区的选择
const select_country = ref<{ value: string; label: string }[]>([]);

const country_1 = ref<SelectProps['options']>(select_country.value);
const country_data = ref<string[]>([]);
// 搜索数据
const Search_content = ref('');

// 付费情况选择
const paymentOptions = ref([
  { value: 'free', label: '免费' },
  { value: 'paid', label: '付费' },
]);
const selectedPayment = ref<string[]>([]);

// 请求数据
const fetchAppList = async () => {
  try {
    let startTime = '';
    let endTime = '';
    if (value1.value && value1.value.length === 2) {
      startTime = value1.value[0].format('YYYY-MM-DD');
      endTime = value1.value[1].format('YYYY-MM-DD');
    }
    const params: any = {
      pageNo: page.value,
      pageSize: pageSize.value,
    };

    // 只添加有值的参数
    if (equipment.value.length > 0) {
      params.platformName = equipment.value.map((item) => (item === 'apple' ? 'App Store' : item));
    }
    if (country_data.value.length > 0) {
      params.countryName = country_data.value;
    }
    if (selectedGameCategory.value.length > 0) {
      params.gameCategory = selectedGameCategory.value;
    }
    if (selectedPayment.value.length > 0) {
      params.paymentType = selectedPayment.value;
    }
    if (startTime) {
      params.startTime = startTime;
    }
    if (endTime) {
      params.endTime = endTime;
    }

    console.log('发送给发行商排名接口的参数:', params);
    const res = await getPublisherInfoApi(params);
    console.log('发行商排名接口返回数据:', res);

    // 处理返回值，添加"选择全部"选项并修改平台名称
    const records = res.records || [];
    Publisher_Date.value = records.map((item: any) => ({
      ...item,
      platform: item.platform === 'apple' ? 'App Store' : item.platform,
    }));

    total.value = res.total || 0;
    if (Publisher_Date.value.length > 0) {
      topAppInfo.value = Publisher_Date.value[0];
    }
  } catch (e) {
    console.error('获取发行商排名数据失败:', e);
    message.error('获取发行商排名数据失败，请稍后重试');
    Publisher_Date.value = [];
    topAppInfo.value = {};
  }
};

const router = useRouter();

const goToPublisherDetail = (record) => {
  router.push({
    name: 'publisherDetails',
    query: {
      id: record.publisherId || record.publisher, // 优先使用publisherId，兼容旧数据
      name: record.publisherName || '', // 传递发行商名称
    },
  });
};

function bijiao() {
  fetchAppList();
}

// 查询方法
const handleSearch = () => {
  page.value = 1;
  fetchAppList();
};

// 辅助函数
const getRankingClass = (ranking: number) => {
  if (ranking === 1) return 'rank-first';
  if (ranking === 2) return 'rank-second';
  if (ranking === 3) return 'rank-third';
  if (ranking <= 10) return 'rank-top10';
  return 'rank-normal';
};

const formatNumber = (num: number | string) => {
  if (!num || num === '-') return '-';
  const number = typeof num === 'string' ? parseFloat(num) : num;
  if (number >= 1000000) {
    return (number / 1000000).toFixed(1) + 'M';
  } else if (number >= 1000) {
    return (number / 1000).toFixed(1) + 'K';
  }
  return number.toLocaleString();
};

const formatCurrency = (amount: number | string) => {
  if (!amount || amount === '-') return '-';
  const number = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (number >= 1000000) {
    return '$' + (number / 1000000).toFixed(1) + 'M';
  } else if (number >= 1000) {
    return '$' + (number / 1000).toFixed(1) + 'K';
  }
  return '$' + number.toLocaleString();
};

const resetFilters = () => {
  value1.value = undefined;
  country_data.value = [];
  equipment.value = [];
  selectedGameCategory.value = [];
  selectedPayment.value = [];
  bijiao();
};
const selectedGameCategory = ref<string[]>([]);

onMounted(() => {
  fetchAppList();
  fetchGameGenres();
  fetchAllDevice();
  loadPublisherOptions();
  fetchCountries();
});

const paginationProp = ref({
  showSizeChanger: false,
  showQuickJumper: true,
  pageSize,
  current: page,
  total,
  showTotal: (total) => `总 ${total} 条`,
  onChange: pageChange,
  onShowSizeChange: pageSizeChange,
});

function pageChange(p, pz) {
  page.value = p;
  pageSize.value = pz;
  fetchAppList();
}
function pageSizeChange(current, size) {
  pageSize.value = size;
  fetchAppList();
}
const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
  if (dates) {
    console.log('From: ', dates[0], ', to: ', dates[1]);
    console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
  } else {
    console.log('Clear');
  }
};

const fetchAllDevice = async () => {
  try {
    const res = await getAllDeviceApi();
    // 处理返回值，将apple转换为App Store，并添加"选择全部"选项
    const deviceOptions = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value === 'apple' ? 'App Store' : item.value,
    }));

    // 添加"选择全部"选项
    equipment_data.value = [...deviceOptions];
  } catch (e) {
    equipment_data.value = [];
  }
};

const publisherSearchValue = ref('');
const publisherOptions = ref<{ label: string; value: string; publisherName?: string }[]>([]);
const publisherLoading = ref(false);
const publisherPage = ref(1);
const publisherPageSize = ref(10);
const publisherTotal = ref(0);

// 发行商搜索
const onPublisherSearch = async (value: string) => {
  publisherSearchValue.value = value;
  publisherPage.value = 1;
  await loadPublisherOptions();
};

// 加载发行商选项
const loadPublisherOptions = async () => {
  try {
    const response = await queryPublisherByNameApi({
      publisherId: publisherSearchValue.value,
      page: publisherPage.value,
      pageSize: publisherPageSize.value
    });

    if (response && response.records) {
      publisherOptions.value = response.records.map((item) => ({
        value: item.publisherId,
        label: item.publisherName,
        publisherName: item.publisherName, // 保存发行商名称，用于详情页面API调用
      }));
    }
  } catch (error) {
    console.error('加载发行商选项失败:', error);
  }
};

// 下拉框滚动到底部时加载更多
const onPopupScroll = (e: Event) => {
  const target = e.target as HTMLElement;
  if (target.scrollTop + target.offsetHeight >= target.scrollHeight - 20) {
    if (publisherOptions.value.length < publisherTotal.value) {
      publisherPage.value++;
      loadPublisherOptions();
    }
  }
};

// 发行商选择
const onPublisherSelect = (value: string) => {
  if (value) {
    // 找到选中的发行商选项，获取发行商名称
    const selectedOption = publisherOptions.value.find(option => option.value === value);
    router.push({
      name: 'publisherDetails',
      query: {
        id: value, // publisherId
        name: selectedOption?.publisherName || '', // publisherName
      },
    });
  }
};

// 标签溢出处理函数
const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { class: 'ellipsis-tag' }, '...');
};
// 国家选择相关函数
const selectAllCountries = () => {
  country_data.value = select_country.value.map((item) => item.value);
};
const handleCountryChange = (value: string[]) => {
  if (value.includes('all')) {
    country_data.value = select_country.value.map((item) => item.value);
  }
};
// 平台选择相关函数
const selectAllEquipment = () => {
  equipment.value = equipment_data.value.map((item) => item.value);
};
const handleEquipmentChange = (value: string[]) => {
  if (value.includes('all')) {
    equipment.value = equipment_data.value.map((item) => item.value);
  }
};
// 游戏类别选择相关函数
const selectAllGames = () => {
  selectedGameCategory.value = games.value.map((item) => item.value);
};
const handleGameCategoryChange = (value: string[]) => {
  if (value.includes('all')) {
    selectedGameCategory.value = games.value.map((item) => item.value);
  }
};
// 付费情况选择相关函数
const selectAllPayment = () => {
  selectedPayment.value = paymentOptions.value.map((item) => item.value);
};
const handlePaymentChange = (value: string[]) => {
  if (value.includes('all')) {
    selectedPayment.value = paymentOptions.value.map((item) => item.value);
  }
};
</script>

<style scoped>
.up_card {
  margin: 24px 2% 0 10px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  border-radius: 16px;
  /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); */
  padding: 32px;
  /* border: 1px solid #2984ca; */
}

/* 头部区域样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  /* border-bottom: 2px solid #e8f4fd; */
}

.publisher-info {
  flex: 1;
}

.publisher-name {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.publisher-id {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
  font-weight: 400;
}

.search-area {
  flex-shrink: 0;
}

.publisher-search {
  width: 280px;
  height: 40px;
}

.publisher-search :deep(.ant-select-selector) {
  border-radius: 8px;
  border: 2px solid #e8f4fd;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

.publisher-search :deep(.ant-select-selector:hover) {
  border-color: #40a9ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.publisher-search :deep(.ant-select-focused .ant-select-selector) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 统计数据区域样式 */
.stats-container {
  margin-top: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.stat-item {
  background: #ffffff;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #d9f7be;
}

.stat-item:hover::before {
  transform: scaleX(1);
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
  word-break: break-all;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .search-area {
    width: 100%;
  }

  .publisher-search {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .up_card {
    margin: 16px 1% 0 5px;
    padding: 20px;
  }

  .publisher-name {
    font-size: 24px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-item {
    padding: 16px;
  }

  .stat-value {
    font-size: 18px;
    text-align: center;

  }

  .table-container {
    margin: 16px 1% 0 5px;
  }

  .filter-section {
    padding: 16px 20px;
  }

  .filter-content {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-item {
    min-width: auto;
    width: 100%;
  }

  .filter-button {
    width: 100%;
    margin-top: 8px;
  }

  .custom-table :deep(.ant-table-content) {
    padding: 16px 20px;
  }
}

.table-container {
  margin: 24px 2% 0 10px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 0;
  border: 1px solid #e8f4fd;
  overflow: hidden;
}

/* 筛选区域样式 */
.filter-section {
  padding: 24px 32px;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.filter-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 2px;
  margin-right: 8px;
}

.filter-content {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-item {
  min-width: 200px;
  height: 36px;
}

.filter-item :deep(.ant-select-selector),
.filter-item :deep(.ant-picker) {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.filter-item :deep(.ant-select-selector:hover),
.filter-item :deep(.ant-picker:hover) {
  border-color: #40a9ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.filter-item :deep(.ant-select-focused .ant-select-selector),
.filter-item :deep(.ant-picker-focused) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.filter-button {
  height: 36px;
  padding: 0 24px;
  border-radius: 8px;
  font-weight: 500;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.filter-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  background: linear-gradient(135deg, #40a9ff, #1890ff);
}

.custom-table {
  width: 100%;
  font-size: 14px;
  margin: 0;
}

.custom-table :deep(.ant-table) {
  margin: 0;
}

.custom-table :deep(.ant-table-container) {
  border-radius: 0 0 16px 16px;
}

.custom-table :deep(.ant-table-content) {
  padding: 24px 32px;
}

/* 表头样式 */
:deep(.custom-table .ant-table-thead > tr > th) {
  background-color: #c2e8f8;
  color: #333;
  font-weight: bold;
  text-align: center;
  padding: 12px 16px;
}

/* 表格单元格样式 */
:deep(.custom-table .ant-table-tbody > tr > td) {
  padding: 12px 16px;
  text-align: center;
  color: #666;
  vertical-align: middle;
  border-bottom: 1px solid #eee;
}

/* 斑马纹样式 */
:deep(.custom-table .ant-table-tbody > tr:nth-child(odd)) {
  background-color: #ffffff;
}
:deep(.custom-table .ant-table-tbody > tr:nth-child(even)) {
  background-color: #dcf2fb;
}

/* 悬停样式 */
:deep(.custom-table .ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff;
}

/* 标题链接样式 */
.title-link {
  color: #018ffb;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.title-link:hover {
  background-color: rgba(1, 143, 251, 0.1);
  color: #0170c9;
}

.empty-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.empty-icon {
  font-size: 32px;
  color: #ccc;
  margin-bottom: 10px;
}

.empty-data-container p {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.empty-data-tip {
  font-size: 14px;
  color: #999;
}



/* 搜索框样式 */
.search_input {
  width: 60%;
  margin-top: 4%;
}

/* 进度条容器样式 */
.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 进度条样式 */
:deep(.ant-progress) {
  flex: 1;
  min-width: 0;
}

/* 进度条数值样式 */
.progress-value {
  min-width: 48px;
  text-align: right;
  font-weight: bold;
}

/* 下载进度条颜色 */
:deep(.download-progress) {
  stroke-color: #40a9ff;
}

/* 收入进度条颜色 */
:deep(.income-progress) {
  stroke-color: #ff9c6e;
}

/* 链接样式 */
a {
  color: #29ade6;
  word-break: break-all;
  text-decoration: none;
}

a:hover {
  color: #1890ff;
}

/* 表格内容样式 */
:deep(.ant-table-cell) {
  font-size: 14px;
  line-height: 1.5;
}

/* 分页器样式 */
:deep(.ant-pagination) {
  margin-top: 16px;
  text-align: right;
}

/* 选择器样式 */
:deep(.ant-select) {
  width: 200px;
}

/* 日期选择器样式 */
:deep(.ant-picker) {
  width: 260px;
}

/* 按钮样式 */
:deep(.ant-btn-primary) {
  background-color: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 级联选择器样式 */
:deep(.ant-cascader) {
  width: 200px;
}

/* ===== 新增美化样式 ===== */

/* 页面容器 */
.publisher-ranking-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 头部信息卡片 */
.header-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  color: white;
  position: relative;
  overflow: hidden;
}

.header-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  position: relative;
  z-index: 1;
}

.top-publisher-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 32px;
}

.publisher-badge {
  display: flex;
  align-items: center;
  gap: 16px;
}

.badge-icon {
  font-size: 48px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.badge-content {
  flex: 1;
}

.top-publisher-name {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.publisher-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.search-section {
  flex-shrink: 0;
  min-width: 320px;
}

.search-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
}

.search-icon {
  font-size: 18px;
}

.modern-search {
  width: 100%;
  height: 44px;
}

.modern-search :deep(.ant-select-selector) {
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  color: white;
  transition: all 0.3s ease;
}

.modern-search :deep(.ant-select-selector:hover) {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.3);
}

.modern-search :deep(.ant-select-focused .ant-select-selector) {
  border-color: white;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.modern-search :deep(.ant-select-selection-placeholder) {
  color: rgba(255, 255, 255, 0.8);
}

/* 统计数据概览 */
.stats-overview {
  background: white;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.stats-header {
  padding: 24px 32px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.stats-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1a1a1a;
}

.title-icon {
  font-size: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 0;
}

.stat-card {
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-right: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.stat-card:hover {
  background: #f8fafc;
  transform: translateY(-2px);
}

.stat-card:last-child,
.stat-card:nth-child(5n) {
  border-right: none;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.games-stat .stat-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.revenue-stat .stat-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.region-stat .stat-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.downloads-stat .stat-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.website-stat .stat-icon {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
  line-height: 1;
}

.website-link {
  font-size: 16px;
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.stat-trend {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

/* 排行榜表格卡片 */
.ranking-table-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 筛选面板 */
.filter-panel {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.filter-header {
  padding: 24px 32px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1f2937;
}

.filter-actions {
  display: flex;
  gap: 12px;
}

.reset-btn {
  color: #6b7280;
  font-size: 14px;
}

.reset-btn:hover {
  color: #374151;
}

.filter-grid {
  padding: 0 32px 24px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 6px;
}

.label-icon {
  font-size: 16px;
}

.modern-filter-input {
  height: 40px;
  border-radius: 8px;
}

.modern-filter-input :deep(.ant-select-selector) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.modern-filter-input :deep(.ant-select-selector:hover) {
  border-color: #667eea;
}

.modern-filter-input :deep(.ant-select-focused .ant-select-selector) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.modern-filter-input :deep(.ant-picker) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
}

.modern-filter-input :deep(.ant-picker:hover) {
  border-color: #667eea;
}

.modern-filter-input :deep(.ant-picker-focused) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.select-all-option {
  font-weight: 600;
  color: #667eea;
}

.search-group {
  display: flex;
  align-items: end;
}

.modern-search-btn {
  height: 40px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.modern-search-btn:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 表格区域 */
.table-section {
  padding: 32px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.table-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1f2937;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.data-count {
  background: #f0f9ff;
  color: #0369a1;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
}

.table-wrapper {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

/* 现代化表格样式 */
.modern-ranking-table :deep(.ant-table-thead > tr > th) {
  background: #f8fafc;
  border-bottom: 2px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  padding: 16px;
  text-align: center;
}

.modern-ranking-table :deep(.ant-table-tbody > tr > td) {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  text-align: center;
}

.modern-ranking-table :deep(.ant-table-tbody > tr:hover > td) {
  background: #f8fafc;
}

/* 排名单元格 */
.ranking-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ranking-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  min-width: 60px;
  justify-content: center;
}

.ranking-badge.rank-first {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #92400e;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.ranking-badge.rank-second {
  background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
  color: #374151;
  box-shadow: 0 2px 8px rgba(192, 192, 192, 0.3);
}

.ranking-badge.rank-third {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #92400e;
  box-shadow: 0 2px 8px rgba(205, 127, 50, 0.3);
}

.ranking-badge.rank-top10 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ranking-badge.rank-normal {
  background: #f3f4f6;
  color: #6b7280;
}

.ranking-crown {
  font-size: 18px;
}

.ranking-number {
  font-size: 16px;
}

/* 发行商单元格 */
.publisher-cell {
  text-align: left;
}

.publisher-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.publisher-name {
  color: #667eea;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
}

.publisher-name:hover {
  color: #5a67d8;
  text-decoration: underline;
}

.publisher-meta {
  display: flex;
  gap: 12px;
}

.publisher-id {
  font-size: 12px;
  color: #9ca3af;
}

/* 进度条单元格 */
.progress-cell {
  padding: 0 8px;
}

.progress-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modern-progress {
  flex: 1;
  min-width: 80px;
}

.progress-text {
  min-width: 48px;
  text-align: right;
  font-weight: 600;
  font-size: 14px;
}

.download-text {
  color: #667eea;
}

.income-text {
  color: #f5576c;
}

/* 数字单元格 */
.number-cell {
  text-align: right;
}

.number-value {
  font-weight: 600;
  color: #1f2937;
}

.income-value {
  color: #059669;
}

.default-cell {
  color: #6b7280;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  font-size: 18px;
  color: #374151;
  margin-bottom: 8px;
  font-weight: 600;
}

.empty-description {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 24px;
}

.retry-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .publisher-ranking-container {
    padding: 16px;
  }

  .header-card {
    padding: 24px;
  }

  .top-publisher-info {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .search-section {
    min-width: auto;
    width: 100%;
  }

  .top-publisher-name {
    font-size: 24px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filter-grid {
    grid-template-columns: 1fr;
    padding: 0 20px 20px;
  }

  .filter-header {
    padding: 16px 20px 12px;
  }

  .table-section {
    padding: 20px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
