// Modal.vue
<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :title="`添加${win_name}`"
    :okText="'加入'"
    @ok="handleOk"
  >
    <div>
      <a-input
        @search="search_it"
        @change="association"
        class="search_input"
        v-model:value="Search_content"
        placeholder="搜索"
      >
        <template #prefix>
          <SearchOutlined :style="{color:'#c9cbce'}" />
        </template>
      </a-input>
      <a-button @click="search_it" class="p_button" type="primary">搜索</a-button>
      <div
        style="margin-top: 20px; max-height: 350px; overflow-y: auto;"
        @scroll="onScroll"
      >
        <div class="search_card" v-for="(item,index) in card_data" :key="item.name">
          <a-row>
            <a-col :span="2">
                <a-checkbox
                  style="margin-top: 15px;margin-left: 10px;"
                  v-model:checked="item.checked"
                  @change="onCheckChange(item)"
                ></a-checkbox>
              </a-col>
            <a-col :span="4">
              <img style="width: 45px;border-radius: 8px 8px 8px 8px;margin-top: 5px;margin-left: 5px;" :src="item.img" alt="">
            </a-col>
            <a-col :span="10">
<div style="font-size: 16px;margin-top: 2px;font-weight: bold;max-width:200px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;" :title="item.name">
  {{ item.name }}
</div>
<div style="font-weight: lighter;max-width:200px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;" :title="item.component_name">
  {{ item.component_name }}
</div>
            </a-col>
          </a-row>
        </div>
        <div v-if="loading" style="text-align:center;margin:10px 0;">
          <a-spin />
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { SearchOutlined } from '@ant-design/icons-vue';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
const win_name = ref('');
const modalData = ref<any>({});
const [register, { closeModal, setModalProps }] = useModalInner(async (data) => {
  win_name.value = data.webb_name;
  modalData.value = data; // 保存所有参数
  setModalProps({
    title: "添加" + win_name.value,
    okText: "加入",
    minHeight: 400,
  });
});

const card_data = ref<any[]>([]);
const page = ref(1);
const pageSize = 20;
const total = ref(0);
const loading = ref(false);
const Search_content = ref('');
function onCheckChange(item: any) {
  const checkedCount = card_data.value.filter(i => i.checked).length;
  if (item.checked && checkedCount > 5) {
    item.checked = false;
    message.warning('最多只能选择5个发行商');
  }
}
// 拉取发行商列表
const fetchPublishers = async (reset = false) => {
  loading.value = true;
  try {
    const res = await defHttp.get({
      url: '/appInfo/queryPublisherByName',
      params: {
        page: page.value,
        pageSize,
        publisherId: Search_content.value,
      },
    });
    const list = res?.records || [];
    total.value = res?.total || 0;
    const newList = list.map((item: any) => ({
      name: item.publisherName || item.publisher,
      component_name: item.publisherId || item.site,
      img: item.logo || new URL('./toux.png', import.meta.url).href,
      checked: false,
    }));
    if (reset) {
      card_data.value = newList;
    } else {
      card_data.value = [...card_data.value, ...newList];
    }
  } catch (e) {
    // 错误处理
  } finally {
    loading.value = false;
  }
};

// 首次加载
onMounted(() => {
  fetchPublishers(true);
});

// 搜索
function search_it() {
  page.value = 1;
  fetchPublishers(true);
}

// 输入联想（可选）
function association() {
  // 可做实时搜索
}

// 下拉加载更多
function onScroll(e: Event) {
  const target = e.target as HTMLElement;
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
    if (card_data.value.length < total.value && !loading.value) {
      page.value += 1;
      fetchPublishers();
    }
  }
}

// 点击确定，回传选中发行商
function handleOk() {
  const selected = card_data.value.filter(item => item.checked);
  
  // 检查是否有重复的发行商
  const existingPublishers = modalData.value?.existingPublishers || [];
  const hasDuplicatePublisher = selected.some(newPublisher => 
    existingPublishers.some(existingPublisher => 
      existingPublisher.name === newPublisher.name
    )
  );

  if (hasDuplicatePublisher) {
    message.warning('不能添加重复的发行商');
    return;
  }

  if (selected.length === 0) {
    message.warning('请至少选择一个发行商');
    return;
  }

  closeModal();
  if (modalData.value && typeof modalData.value.onOk === 'function') {
    modalData.value.onOk(selected);
  }
}
</script>

<style>
.search_card{
    width: 90%;
    height: 55px;
    background-color: #e2e8eb;
    margin-left: 5%;
    margin-top: 1%;
    border-radius:8px 8px 8px 8px;
}
.search_input{
      width:60%;
      height:40px;
      border-radius:10px 0px 0px 10px;
      border: 1px solid #e2e8eb;
      margin-top: 5%;
      margin-left:12%
}

.p_button{
    height: 40px;
    width: 60px;
    position: relative;
    top: 4px;
    border-radius:0px 10px 10px 0px;
}
</style>
