<template>
   <div class="TopBar">
    <a-row>
      <a-col span="2">
        <img
          style="width: 82px;height: 82px;margin-top: 15px;margin-bottom: 15px;margin-left: 10px"
          :src="publisherInfo.logo || '/src/assets/images/game1.png'"
        />
      </a-col>
      <a-col span="3" style="margin-top: 15px;">
        <span style="font-size: 24px;font-weight: 500;word-break: break-all;max-width: 200px;display: inline-block;">{{ gameDetail.name || publisherInfo.name || '发行商名称' }}</span>
        <!-- <span style="display: flex;color: rgba(147, 150, 153, 1);margin-top: 10px;">{{ publisherInfo.companyName || '公司名称' }}</span> -->
      </a-col>
      <a-col span="1">
        <span style="writing-mode: vertical-lr;margin-top: 30px;font-size: 18px;border-bottom: 2px solid rgba(8, 147, 207, 1);margin-bottom: 5px;letter-spacing: 10px;">简</span>
        <span style="writing-mode: vertical-lr;font-size: 18px;display: flex;">介</span>
      </a-col>
      <a-col span="15" style="top: 22px;">
        <p style="text-indent: 2em;">
          {{ publisherInfo.introduction || '暂无简介' }}
        </p>
      </a-col>
      <a-col span="3" style="left: 30px;">
        <a-button
          :style="focusStatus
            ? 'top: 40px; background: #52c41a; width: 113px; height: 40px; color: white;'
            : 'top: 40px; background: rgba(41, 173, 230, 1); width: 113px; height: 40px; color: white;'"
          @click="handleFocusClick"
        >
          {{ focusStatus ? '取消关注' : '+ 关注' }}
        </a-button>
      </a-col>
    </a-row>
  </div>
  <div class="MBar">
    <a-row style="margin-top: 10px;">
      <a-col :span="8" justify="center" align="middle" style="border-right: 1px solid rgba(196, 200, 204, 1);">
        <span>发行游戏数</span>
        <span style="display:flex;justify-content: center;margin-top: 20px;margin-bottom: 20px;font-size: 30px;">
          {{ gameDetail.gameNumber ?? '--' }}
        </span>
      </a-col>
      <a-col :span="8" justify="center" align="middle" style="border-right: 1px solid rgba(196, 200, 204, 1);">
        <span>上月下载量</span>
        <span style="display:flex;justify-content: center;margin-top: 20px;margin-bottom: 20px;color: rgba(255, 141, 26, 1);font-size: 30px;">
          {{ gameDetail.downloadTimeLastMonth ?? '--' }}
        </span>
      </a-col>
      <a-col :span="8" justify="center" align="middle">
        <span>上月收入</span>
        <span style="display:flex;justify-content: center;margin-top: 20px;margin-bottom: 20px;color: rgba(255, 87, 51, 1);font-size: 30px;">
          {{ gameDetail.incomeSumLastMonth ?? '--' }}
        </span>
      </a-col>
    </a-row>
  </div>

  <div class="BTable">
    <div style="margin: 20px 0;">
      <h3>游戏列表</h3>
          <a-table
      :columns="[
        { title: '游戏名称', dataIndex: 'appName', key: 'appName' },
        { title: '国家', dataIndex: 'countryId', key: 'countryId' },
        { title: '平台', dataIndex: 'storeType', key: 'storeType' },
        { title: '版本', dataIndex: 'version', key: 'version' },
        { title: '上架时间', dataIndex: 'releaseTime', key: 'releaseTime' },
        { title: '最新更新时间', dataIndex: 'lastReleaseTime', key: 'lastReleaseTime' },
        { title: '下载量', dataIndex: 'downloadTimes', key: 'downloadTimes' },
        { title: '收入', dataIndex: 'income', key: 'income' },
      ]"
      :data-source="gameDetail.gameList || []"
      row-key="uuid"
      size="small"
      bordered
      :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'downloadTimes' || column.key === 'income'">
          {{ (record[column.dataIndex] === null || record[column.dataIndex] === undefined || record[column.dataIndex] === '') ? '-' : record[column.dataIndex] }}
        </template>
        <img v-else-if="column.key === 'icon'" :src="record.icon" style="width:32px;height:32px;" />
      </template>
    </a-table>
    </div>
    <!-- <div id="details" class="chart"></div> -->
     <div class="input">
      <div class="header1"> 近七日趋势 </div>
      <div style="margin: 30px 0 10px 0; padding: 16px; background: #f9f9f9; border-radius: 8px;">
        <a-row :gutter="16" align="middle">
          <a-col :span="5">
            <a-range-picker
              v-model:value="trendDateRange"
              :presets="trendRangePresets"
              style="width: 100%;"
              allow-clear
              :getPopupContainer="triggerNode => triggerNode.parentNode"
            />
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="trendCountry"
              mode="multiple"
              allowClear
              placeholder="选择国家"
              style="width: 100%;"
              :max-tag-count="1"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handleCountryChange"
              :getPopupContainer="triggerNode => triggerNode.parentNode"
            >
              <a-select-option value="all" @click="selectAllCountries">选择全部</a-select-option>
              <a-select-option v-for="country in countryOptions" :key="country.value" :value="country.value">
            {{ country.label }}
          </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="trendPlatform"
              mode="multiple"
              allowClear
              placeholder="选择平台"
              style="width: 100%;"
              :max-tag-count="1"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handlePlatformChange"
              :getPopupContainer="triggerNode => triggerNode.parentNode"
            >
              <a-select-option value="all" @click="selectAllPlatforms">选择全部</a-select-option>
              <a-select-option v-for="item in platformOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="trendGenre"
              mode="multiple"
              allowClear
              placeholder="选择游戏类别"
              style="width: 100%;"
              :max-tag-count="1"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handleGenreChange"
              :getPopupContainer="triggerNode => triggerNode.parentNode"
            >
              <a-select-option value="all" @click="selectAllGenres">选择全部</a-select-option>
              <a-select-option v-for="game in genreOptions" :key="game.value" :value="game.value">
            {{ game.label }}
          </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="trendIncome"
              mode="multiple"
              allowClear
              placeholder="选择收入"
              style="width: 100%;"
              :max-tag-count="1"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handleIncomeChange"
              :getPopupContainer="triggerNode => triggerNode.parentNode"
            >
              <a-select-option value="all" @click="selectAllIncomes">选择全部</a-select-option>
              <a-select-option v-for="item in trendIncomeOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="3">
            <a-button type="primary" @click="handleSearch" :loading="loading">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
          </a-col>
        </a-row>
      </div>
      <div class="chart-container">
        <div ref="trendChartRef" class="chart"></div>
      </div>
    </div>
  </div>
  
  </template>
    
  <script lang="ts" name="multi-table-demo" setup>
  import { ActionItem, BasicColumn, BasicTable, FormSchema, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import dayjs ,{ Dayjs } from 'dayjs';
  import { onMounted, onUnmounted, ref, watch,nextTick, computed, h } from 'vue';
  import type { TreeSelectProps } from 'ant-design-vue';
  import { message, TreeSelect } from 'ant-design-vue';
  import * as echarts from 'echarts';
  import { useRoute } from 'vue-router';
  import {
  getPublisherGameInfoDetailApi,
  getPublisherInfoDetailApi,
  getPublisherInfo7DetailApi,
  focusPublisherApi,
  unfocusPublisherApi,
  queryFocusStatusApi,
  getAllGenreApi,
  getAllCountryApi,
  getAllDeviceApi
} from '@/api/store-information/publisher-detail/index';

  import { SearchOutlined } from '@ant-design/icons-vue';

  // 新增：发行商详情数据
  
  const publisherInfo = ref<any>({});
  const route = useRoute();
  const gameDetail = ref<any>({});
  let trendChart: any = null;
  const fetchPublisherDetail = async () => {
  try {
    // 优先使用传递的publisherName，如果没有则使用id（兼容旧版本）
    const publisherName = (route.query.name || route.query.id) as string;
    if (!publisherName) return;
    const res = await getPublisherGameInfoDetailApi(publisherName);
    publisherInfo.value = res || {};
  } catch (e) {
    publisherInfo.value = {};
  }
  };
  // 新增：七日趋势相关
type RangeValue = [Dayjs, Dayjs];
const trendDateRange = ref<[Dayjs, Dayjs]>([dayjs('2025-04-19'), dayjs('2025-04-19')]);
const trendRangePresets = ref([
  { label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
  { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
  { label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
  { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
  { label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
  { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
  { label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
  { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
]);

const trendCountry = ref<string[]>([]);

const countryOptions = ref<{ value: string; label: string }[]>([]);

const trendPlatform = ref<string[]>([]);
const platformOptions = ref<{ value: string; label: string }[]>([]);
const fetchPlatforms = async () => {
  try {
    const res = await getAllDeviceApi();
    // 格式化平台数据，确保与之前一致
    platformOptions.value = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value === 'apple' ? 'App Store' : 
             item.value === 'google' ? 'Google Play' : 
             item.value
    }));
  } catch (e) {
    platformOptions.value = [];
  }
};
interface GameType {
  value: string;
  label: string;
}

const trendGenre = ref<string[]>([]);
const genreOptions = ref<GameType[]>([]);
// 获取国家数据
const fetchCountries = async () => {
  try {
    const res = await getAllCountryApi();
    
    if (res && Array.isArray(res)) {
      // 过滤掉无效的国家数据
      countryOptions.value = res
        .filter((item: any) => {
          return item && 
                 typeof item === 'object' &&
                 item.value &&
                 typeof item.value === 'string' &&
                 item.value.trim() !== '' &&
                 item.value !== '5' &&
                 item.value !== '6'; // 过滤掉数字ID
        })
        .map((item: any) => ({
          value: item.value.trim(),
          label: item.value.trim(),
        }));
    } else {
      throw new Error('API返回数据格式不正确');
    }
  } catch (e) {
    // API失败时使用默认国家列表
    countryOptions.value = [
      { value: '菲律宾', label: '菲律宾' },
      { value: '柬埔寨', label: '柬埔寨' },
      { value: '马来西亚', label: '马来西亚' },
      { value: '泰国', label: '泰国' },
      { value: '文莱', label: '文莱' },
      { value: '新加坡', label: '新加坡' },
      { value: '印度尼西亚', label: '印度尼西亚' },
      { value: '越南', label: '越南' },
      { value: '缅甸', label: '缅甸' },
      { value: '中国台湾', label: '中国台湾' },
      { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
    ];
    console.error('获取国家数据失败:', e);
  }
};
const fetchGameTypes = async () => {
  try {
    const response = await getAllGenreApi();
    if (response && Array.isArray(response)) {
      genreOptions.value = [
        ...response.map(item => ({
          value: item.value || '',
          label: item.label || item.value || ''
        }))
      ];
    }
  } catch (error) {
    console.error('获取游戏类型失败:', error);
  }
};

const games = ref<{ value: string; label: string }[]>([]);
      const fetchGameGenres = async () => {
        try {
          const res = await getAllGenreApi();
          games.value = (res || []).map((item: any) => ({
            value: item.value,
            label: item.value,
          }));
        } catch (e) {
          games.value = [];
        }
      };
const trendType = ref<'收入' | '下载量'>('收入');
const trendTypeOptions = [
  { value: '收入', label: '收入' },
  { value: '下载量', label: '下载量' },
];

const trendChartData = ref<{ date: string; value: number }[]>([]);

const hasSearched = ref(false);
const loading = ref(false);
const trendChartRef = ref<HTMLDivElement | null>(null);

const trendIncome = ref<string[]>([]);
const trendIncomeOptions = [
  { value: 'free', label: '免费' },
  { value: 'paid', label: '付费' },
];

const hasData = computed(() => {
  return trendChartData.value.length > 0 && trendChartData.value.some(item => item.value > 0);
});

const initTrendChart = () => {
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value);
    trendChart.setOption(getTrendChartOption());
  }
};

const updateTrendChart = () => {
  if (trendChart) {
    trendChart.setOption(getTrendChartOption());
    trendChart.resize();
  }
};

const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { style: { color: '#666' } }, `+${omittedValues.length}...`);
};

const selectAllCountries = () => {
  trendCountry.value = countryOptions.map(item => item.value);
};

const selectAllPlatforms = () => {
  trendPlatform.value = platformOptions.map(item => item.value);
};

const selectAllGenres = () => {
  trendGenre.value = genreOptions.value.map(item => item.value);
};

const selectAllIncomes = () => {
  trendIncome.value = trendIncomeOptions.map(item => item.value);
};

const handleCountryChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllCountries();
  }
};

const handlePlatformChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllPlatforms();
  }
};

const handleGenreChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllGenres();
  }
};

const handleIncomeChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllIncomes();
  }
};

const handleSearch = async () => {
  try {
    loading.value = true;
    hasSearched.value = true;
    let startTime = '';
    let endTime = '';
    if (trendDateRange.value && trendDateRange.value.length === 2) {
      startTime = trendDateRange.value[0].format('YYYY-MM-DD');
      endTime = trendDateRange.value[1].format('YYYY-MM-DD');
    }
    const response = await getPublisherInfo7DetailApi({
      countryName: trendCountry.value,
      deviceName: trendPlatform.value,
      gameCategory: trendGenre.value,
      isPaid: trendIncome.value,
      startTime: startTime,
      endTime: endTime,
      publisherId: route.query.id as string,
    });
    
    // 更新图表数据
    if (response && Array.isArray(response)) {
      trendChartData.value = response.map(item => {
        const date = Object.keys(item)[0];
        const value = parseInt(item[date]) || 0;
        return {
          date: date,
          value: value
        };
      });
      
      // 确保数据更新后再更新图表
      await nextTick();
      updateTrendChart();
    } else {
      trendChartData.value = [];
      updateTrendChart();
    }
  } catch (error) {
    console.error('获取趋势数据失败:', error);
    trendChartData.value = [];
    updateTrendChart();
  } finally {
    loading.value = false;
  }
};

const getTrendChartOption = () => {
  const dates = trendChartData.value.map(item => item.date);
  const values = trendChartData.value.map(item => item.value);
  const hasData = trendChartData.value.length > 0 && values.some(v => v > 0);

  const option: any = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45,
        formatter: (value: string) => value
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'bar',
        data: values,
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  };
  return option;
};


  const fetchGameDetail = async () => {
  try {
    // 优先使用传递的publisherName，如果没有则使用id（兼容旧版本）
    const publisherName = (route.query.name || route.query.id) as string;
    console.log(publisherName);
    const res = await getPublisherInfoDetailApi(publisherName);
    gameDetail.value = res || {};
  } catch (e) {
    gameDetail.value = {};
  }
};
 onMounted(() => {
  fetchGameGenres();
  fetchPublisherDetail();
  fetchGameDetail();
  fetchGameTypes();
  fetchCountries();
  fetchPlatforms();
  handleSearch()

  // 图表初始化可根据 gameDetail.value.gameList 数据动态渲染
  // myChart1 = echarts.init(document.getElementById('details'));
  // 示例静态数据，实际可用 gameDetail.value.gameList 处理
  // myChart1.setOption({
  //   xAxis: {
  //     type: 'category',
  //     data: (gameDetail.value.gameList || []).map(item => item.appName) || [],
  //   },
  //   yAxis: { type: 'value' },
  //   series: [
  //     {
  //       data: (gameDetail.value.gameList || []).map(item => Number(item.totalDownloads) || 0),
  //       type: 'bar',
  //       barWidth: '40%',
  //     },
  //   ],
  // });
    nextTick(() => {
    initTrendChart();
    window.addEventListener('resize', () => {
      if (trendChart) {
        trendChart.resize();
      }
    });
  });
});
  onUnmounted(() => {
    // myChart1?.dispose();

  if (trendChart) {
    trendChart.dispose();
    trendChart = null;
  }
  window.removeEventListener('resize', () => {
    if (trendChart) {
      trendChart.resize();
    }
  });
  });
  
  const options = ref([
    {
      value: 'china',
      label: '中国',
    },
    {
      value: 'jpn',
      label: '日本',
    },
  ]);
  const handleChange = (value) => {
    console.log(value); // { key: "lucy", label: "Lucy (101)" }
  };
  const value = ref({
    value: 'nation',
    label: '请选择国家/地区',
  });
  
  const SHOW_PARENT = TreeSelect.SHOW_PARENT;
  const treeData: TreeSelectProps['treeData'] = [
    {
      label: '手机端',
      value: 'mc',
      children: [
        {
          label: 'IPhone',
          value: 'iPhone',
          children: [
            {
              label: 'iPhone 15',
              value: 'iPhone 15',
            },
            {
              label: 'iPhone 15 Pro',
              value: 'iPhone 15 pro',
            },
          ],
        },
        {
          label: '华为',
          value: 'huawei',
          children: [
            {
              label: 'HUAWEI Mate 70',
              value: 'HUAWEI Mate 70',
            },
            {
              label: 'HUAWEI Mate X6',
              value: 'HUAWEI Mate X6',
            },
          ],
        },
      ],
    },
    {
      label: '电脑端',
      value: 'pc',
  
      children: [
        {
          label: 'Child Node3',
          value: '0-1-0',
        },
        {
          label: 'Child Node4',
          value: '0-1-1',
        },
        {
          label: 'Child Node5',
          value: '0-1-2',
        },
      ],
    },
  ];
  
  watch(value, () => {
    console.log(value.value);
  });
  
  const options2 = ref([
    {
      value: 'jingji',
      label: '竞技',
    },
    {
      value: 'Android',
      label: '安卓',
    },
  ]);
  const value2 = ref({
    value: 'shebei',
    label: '请选择游戏类别',
  });
  
  const options3 = ref([
    {
      value: 'downloads',
      label: '下载量',
    },
    {
      value: 'income',
      label: '净收入',
    },
  ]);
  const value3 = ref({
    value: 'download',
    label: '下载量',
  });
  
  const schemas: FormSchema[] = [
    {
      label: '日期选择',
      field: 'dateSelect',
      component: 'DatePicker',
      componentProps: {
        //日期格式化，页面上显示的值
        format: 'YYYY-MM-DD',
        //返回值格式化（绑定值的格式）
        valueFormat: 'YYYY-MM-DD',
        //是否显示今天按钮
        showToday: true,
        //不可选择日期
        disabledDate: (currentDate) => {
          let date = dayjs(currentDate).format('YYYY-MM-DD');
          let nowDate = dayjs(new Date()).format('YYYY-MM-DD');
          //当天不可选择
          if (date == nowDate) {
            return true;
          }
          return false;
        },
      },
    },
  ];
  
  //定义表格列字段
  const columns1: BasicColumn[] = [
    {
      title: '游戏名称',
      dataIndex: 'gamename',
      key: 'gamename',
    },
    {
      title: '上新时间',
      dataIndex: 'uptime',
      key: 'uptime',
    },
    {
      title: '上月下载量',
      dataIndex: 'downloads',
      key: 'downloads',
    },
    {
      title: '上月收入',
      dataIndex: 'income',
      key: 'income',
    },
  ];
  const columns2: BasicColumn[] = [
    {
      title: '游戏名称',
      dataIndex: 'gamename',
      key: 'gamename',
    },
    {
      title: '更新时间',
      dataIndex: 'uptime',
      key: 'uptime',
    },
    {
      title: '上月下载量',
      dataIndex: 'downloads',
      key: 'downloads',
    },
    {
      title: '上月收入',
      dataIndex: 'income',
      key: 'income',
    },
  ];
  const columns3: BasicColumn[] = [
    {
      title: '游戏名称',
      dataIndex: 'gamename',
      key: 'gamename',
    },
    {
      title: '国家',
      dataIndex: 'nation',
      key: 'nation',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
    },
    {
      title: '最新更新日期',
      dataIndex: 'uptime',
      key: 'uptime',
    },
    {
      title: '发布日期',
      dataIndex: 'postdate',
      key: 'postdate',
    },
    {
      title: '上月下载量',
      dataIndex: 'downloads',
      key: 'downloads',
    },
    {
      title: '上月收入',
      dataIndex: 'income',
      key: 'income',
    },
  ];
  
  /** useListPage 是整个框架的核心用于表格渲染，里边封装了很多公共方法；
   * 平台通过此封装，简化了代码，支持自定义扩展*/
  // 通过hook useListPage渲染表格（设置dataSource、columns、actionColumn等参数）
  const { tableContext: tableContext1 } = useListPage({
    designScope: 'table1',
    tableProps: {
      title: '上新游戏',
      dataSource: [
        {
          key: '1',
          gamename: '银河帝国',
          uptime: '2024/6/7',
          downloads: '319w',
          income: '21w',
        },
        {
          key: '2',
          gamename: '银河游侠',
          uptime: '2024/9/5',
          downloads: '663w',
          income: '34w',
        },
        {
          key: '3',
          gamename: '幻影街',
          uptime: '2024/2/6',
          downloads: '927w',
          income: '997w',
        },
        {
          key: '4',
          gamename: '银河边境',
          uptime: '2024/9/5',
          downloads: '762w',
          income: '13w',
        },
      ],
      columns: columns1,
      size: 'small',
      actionColumn: {
        width: 120,
      },
      showActionColumn: false,
      striped: true,
      showTableSetting: false,
      pagination: false,
    },
  });
  // BasicTable绑定注册
  const [registerTable1] = tableContext1;
  
  const { tableContext: tableContext2 } = useListPage({
    designScope: 'table2',
    tableProps: {
      title: '更新游戏',
      dataSource: [
        {
          key: '1',
          gamename: '银河帝国',
          uptime: '2024/6/7',
          downloads: '319w',
          income: '21w',
        },
        {
          key: '2',
          gamename: '银河游侠',
          uptime: '2024/9/5',
          downloads: '663w',
          income: '34w',
        },
        {
          key: '3',
          gamename: '幻影街',
          uptime: '2024/2/6',
          downloads: '927w',
          income: '997w',
        },
        {
          key: '4',
          gamename: '银河边境',
          uptime: '2024/9/5',
          downloads: '762w',
          income: '13w',
        },
      ],
      columns: columns2,
      size: 'small',
      actionColumn: {
        width: 120,
      },
      showActionColumn: false,
      striped: true,
      showTableSetting: false,
      pagination: false,
    },
  });
  // BasicTable绑定注册
  const [registerTable2] = tableContext2;
  
  const { tableContext: tableContext3 } = useListPage({
    designScope: 'table2',
    tableProps: {
      title: '更新游戏',
      dataSource: [
        {
          key: '1',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
        {
          key: '2',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
        {
          key: '3',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
        {
          key: '4',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
        {
          key: '5',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
      ],
      columns: columns3,
      size: 'small',
      actionColumn: {
        width: 120,
      },
      showActionColumn: false,
      striped: true,
      showTableSetting: false,
      pagination: false,
    },
  });
  // BasicTable绑定注册
  const [registerTable3] = tableContext3;
  /**
   * 操作栏
   */
  // function getTableAction(record): ActionItem[] {
  //   return [
  //     {
  //       label: '编辑',
  //       onClick: handleEdit.bind(null, record),
  //     },
  //   ];
  // }
  
  // function handleEdit(record) {
  //   console.log(record);
  // }

const focusStatus = ref(false); // false: 未关注，true: 已关注

const fetchFocusStatus = async () => {
  try {
    // 优先使用传递的publisherName，如果没有则使用id（兼容旧版本）
    const publisherName = (route.query.name || route.query.id) as string;
    if (!publisherName) return;
    const res = await queryFocusStatusApi(publisherName);
    // result为"已关注"或"未关注"
    focusStatus.value = res === '已关注';
  } catch {
    focusStatus.value = false;
  }
};

const handleFocusClick = async () => {
  // 优先使用传递的publisherName，如果没有则使用id（兼容旧版本）
  const publisherName = (route.query.name || route.query.id) as string;
  if (!publisherName) return;
  if (focusStatus.value) {
    // 已关注，点击取关
    try {
      await unfocusPublisherApi(publisherName);
      focusStatus.value = false;
      message.success('已取消关注');
    } catch {
      message.error('取消关注失败');
    }
  } else {
    // 未关注，点击关注
    try {
      await focusPublisherApi(publisherName);
      focusStatus.value = true;
      message.success('关注成功');
    } catch {
      message.error('关注失败');
    }
  }
};

onMounted(() => {
  fetchFocusStatus();
});
  </script>
  <style>
  .ant-table-wrapper .ant-table-thead > tr > th {
    background: rgba(80, 189, 235, 0.35);
  }
  .jeecg-basic-table-row__striped td {
    background: rgba(80, 189, 235, 0.2);
  }
  .ant-select-single.ant-select-show-arrow {
    color: rgba(196, 200, 204, 1);
  }
  .TopBar {
    border-radius: 8px;
    margin: 10px 10px;
    height: 160px;
    background: #fff;
    padding: 10px;
    box-shadow: 0px 0px 0px rgba(77, 85, 117, 0.05), 0px 3px 7px rgba(77, 85, 117, 0.05), 0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03), 0px 20px 20px rgba(77, 85, 117, 0.01), 0px 35px 30px rgba(77, 85, 117, 0);
  }
  .MBar {
    border-radius: 8px;
    margin: 10px;
    background: #fff;
    padding: 10px;
    box-shadow: 0px 0px 0px rgba(77, 85, 117, 0.05), 0px 3px 7px rgba(77, 85, 117, 0.05), 0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03), 0px 20px 20px rgba(77, 85, 117, 0.01), 0px 35px 30px rgba(77, 85, 117, 0);
  }
  .MTable {
    border-radius: 8px;
    margin: 10px 10px ;
    background: #fff;
    padding: 10px;
    width: 49%;
    display: inline-block;
    box-shadow: 0px 0px 0px rgba(77, 85, 117, 0.05), 0px 3px 7px rgba(77, 85, 117, 0.05), 0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03), 0px 20px 20px rgba(77, 85, 117, 0.01), 0px 35px 30px rgba(77, 85, 117, 0);
  }
  .BTable {
    border-radius: 8px;
    margin: 10px 10px;
    background: #fff;
    padding: 10px;
    box-shadow: 0px 0px 0px rgba(77, 85, 117, 0.05), 0px 3px 7px rgba(77, 85, 117, 0.05), 0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03), 0px 20px 20px rgba(77, 85, 117, 0.01), 0px 35px 30px rgba(77, 85, 117, 0);
  }
  .chart {
    width: 100%;
    height: 500px; /* 保持相对高度 */
  }
  .empty-trend {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #fafafa;
    border-radius: 8px;
    padding: 40px 0;
  }

  .empty-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
  }

  .empty-tip {
    font-size: 14px;
    color: #999;
  }
  
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    background: #fff;
    border-radius: 8px;
  }
  </style>