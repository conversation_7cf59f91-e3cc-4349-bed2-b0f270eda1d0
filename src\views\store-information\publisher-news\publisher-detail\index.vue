<template>
  <div class="publisher-detail-container">
    <!-- 发行商信息卡片 -->
    <div class="publisher-info-card">
      <div class="publisher-header">
        <div class="publisher-avatar">
          <img
            :src="publisherInfo.logo || '/src/assets/images/game1.png'"
            alt="发行商Logo"
          />
        </div>
        <div class="publisher-basic-info">
          <h1 class="publisher-name">{{
            gameDetail.publisherName ||
            publisherInfo.publisherName ||
            gameDetail.name ||
            publisherInfo.name ||
            gameDetail.publisher ||
            publisherInfo.publisher ||
            route.query.name ||
            '发行商名称'
          }}</h1>
          <p class="publisher-company" v-if="publisherInfo.companyName">
            {{ publisherInfo.companyName }}
          </p>
        </div>
        <div class="publisher-actions">
          <a-button
            :type="focusStatus ? 'default' : 'primary'"
            :class="['focus-btn', { 'focused': focusStatus }]"
            @click="handleFocusClick"
            size="large"
          >
            <template #icon>
              <span v-if="!focusStatus">+</span>
              <span v-else>✓</span>
            </template>
            {{ focusStatus ? '已关注' : '关注' }}
          </a-button>
        </div>
      </div>

      <div class="publisher-intro" v-if="publisherInfo.introduction">
        <div class="intro-label">
          <span>简介</span>
        </div>
        <div class="intro-content">
          <p>{{ publisherInfo.introduction }}</p>
        </div>
      </div>
    </div>
    <!-- 统计数据卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon games-icon">
          <i class="icon">🎮</i>
        </div>
        <div class="stat-content">
          <div class="stat-label">发行游戏数</div>
          <div class="stat-value primary">{{ gameDetail.gameNumber ?? '--' }}</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon downloads-icon">
          <i class="icon">📱</i>
        </div>
        <div class="stat-content">
          <div class="stat-label">上月下载量</div>
          <div class="stat-value warning">{{ gameDetail.downloadTimeLastMonth ?? '--' }}</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon revenue-icon">
          <i class="icon">💰</i>
        </div>
        <div class="stat-content">
          <div class="stat-label">上月收入</div>
          <div class="stat-value danger">{{ gameDetail.incomeSumLastMonth ?? '--' }}</div>
        </div>
      </div>
    </div>

    <!-- 游戏列表卡片 -->
    <div class="games-table-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="title-icon">🎯</i>
          游戏列表
        </h3>
        <div class="game-count" v-if="gameDetail.gameList && gameDetail.gameList.length">
          共 {{ gameDetail.gameList.length }} 款游戏
        </div>
      </div>

      <div class="table-container">
        <a-table
          :columns="[
            { title: '游戏名称', dataIndex: 'appName', key: 'appName', width: 200, ellipsis: true },
            { title: '国家', dataIndex: 'countryId', key: 'countryId', width: 100 },
            { title: '平台', dataIndex: 'storeType', key: 'storeType', width: 100 },
            { title: '版本', dataIndex: 'version', key: 'version', width: 120 },
            { title: '上架时间', dataIndex: 'releaseTime', key: 'releaseTime', width: 120 },
            { title: '最新更新', dataIndex: 'lastReleaseTime', key: 'lastReleaseTime', width: 120 },
            { title: '下载量', dataIndex: 'downloadTimes', key: 'downloadTimes', width: 120, align: 'right' },
            { title: '收入', dataIndex: 'income', key: 'income', width: 120, align: 'right' },
          ]"
          :data-source="gameDetail.gameList || []"
          row-key="uuid"
          size="middle"
          :pagination="{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }"
          class="modern-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'downloadTimes' || column.key === 'income'">
              <span class="number-cell">
                {{ (record[column.dataIndex] === null || record[column.dataIndex] === undefined || record[column.dataIndex] === '') ? '-' : record[column.dataIndex] }}
              </span>
            </template>
            <template v-else-if="column.key === 'appName'">
              <div class="game-name-cell">
                <img v-if="record.icon" :src="record.icon" class="game-icon" />
                <span class="game-name" :title="record.appName">{{ record.appName }}</span>
              </div>
            </template>
            <template v-else-if="column.key === 'storeType'">
              <a-tag :color="record.storeType === 'App Store' ? 'blue' : 'green'">
                {{ record.storeType }}
              </a-tag>
            </template>
          </template>

          <template #emptyText>
            <div class="empty-state">
              <div class="empty-icon">📱</div>
              <div class="empty-text">暂无游戏数据</div>
              <div class="empty-tip">该发行商暂未发布游戏</div>
            </div>
          </template>
        </a-table>
      </div>
    </div>
    <!-- 趋势图表卡片 -->
    <div class="trend-chart-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="title-icon">📈</i>
          近期趋势分析
        </h3>
      </div>

      <div class="filter-section">
        <a-row :gutter="[16, 16]" align="middle">
          <a-col :xs="24" :sm="12" :md="6" :lg="5">
            <div class="filter-item">
              <label class="filter-label">时间范围</label>
              <a-range-picker
                v-model:value="trendDateRange"
                :presets="trendRangePresets"
                style="width: 100%;"
                allow-clear
                :getPopupContainer="triggerNode => triggerNode.parentNode"
              />
            </div>
          </a-col>

          <a-col :xs="12" :sm="6" :md="4" :lg="4">
            <div class="filter-item">
              <label class="filter-label">国家</label>
              <a-select
                v-model:value="trendCountry"
                mode="multiple"
                allowClear
                placeholder="选择国家"
                style="width: 100%;"
                :max-tag-count="1"
                :max-tag-placeholder="maxTagPlaceholder"
                @change="handleCountryChange"
                :getPopupContainer="triggerNode => triggerNode.parentNode"
              >
                <a-select-option value="all" @click="selectAllCountries">选择全部</a-select-option>
                <a-select-option v-for="country in countryOptions" :key="country.value" :value="country.value">
                  {{ country.label }}
                </a-select-option>
              </a-select>
            </div>
          </a-col>

          <a-col :xs="12" :sm="6" :md="4" :lg="4">
            <div class="filter-item">
              <label class="filter-label">平台</label>
              <a-select
                v-model:value="trendPlatform"
                mode="multiple"
                allowClear
                placeholder="选择平台"
                style="width: 100%;"
                :max-tag-count="1"
                :max-tag-placeholder="maxTagPlaceholder"
                @change="handlePlatformChange"
                :getPopupContainer="triggerNode => triggerNode.parentNode"
              >
                <a-select-option value="all" @click="selectAllPlatforms">选择全部</a-select-option>
                <a-select-option v-for="item in platformOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </a-col>

          <a-col :xs="12" :sm="6" :md="4" :lg="4">
            <div class="filter-item">
              <label class="filter-label">游戏类别</label>
              <a-select
                v-model:value="trendGenre"
                mode="multiple"
                allowClear
                placeholder="选择类别"
                style="width: 100%;"
                :max-tag-count="1"
                :max-tag-placeholder="maxTagPlaceholder"
                @change="handleGenreChange"
                :getPopupContainer="triggerNode => triggerNode.parentNode"
              >
                <a-select-option value="all" @click="selectAllGenres">选择全部</a-select-option>
                <a-select-option v-for="game in genreOptions" :key="game.value" :value="game.value">
                  {{ game.label }}
                </a-select-option>
              </a-select>
            </div>
          </a-col>

          <a-col :xs="12" :sm="6" :md="4" :lg="4">
            <div class="filter-item">
              <label class="filter-label">收入类型</label>
              <a-select
                v-model:value="trendIncome"
                mode="multiple"
                allowClear
                placeholder="选择收入"
                style="width: 100%;"
                :max-tag-count="1"
                :max-tag-placeholder="maxTagPlaceholder"
                @change="handleIncomeChange"
                :getPopupContainer="triggerNode => triggerNode.parentNode"
              >
                <a-select-option value="all" @click="selectAllIncomes">选择全部</a-select-option>
                <a-select-option v-for="item in trendIncomeOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="4" :lg="3">
            <div class="filter-item">
              <a-button type="primary" @click="handleSearch" :loading="loading" block class="search-btn">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
            </div>
          </a-col>
        </a-row>
      </div>

      <div class="chart-wrapper">
        <div ref="trendChartRef" class="trend-chart"></div>
      </div>
    </div>
  </div>
  
  </template>
    
  <script lang="ts" name="multi-table-demo" setup>
  import { ActionItem, BasicColumn, BasicTable, FormSchema, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import dayjs ,{ Dayjs } from 'dayjs';
  import { onMounted, onUnmounted, ref, watch,nextTick, computed, h } from 'vue';
  import type { TreeSelectProps } from 'ant-design-vue';
  import { message, TreeSelect } from 'ant-design-vue';
  import * as echarts from 'echarts';
  import { useRoute } from 'vue-router';
  import {
  getPublisherGameInfoDetailApi,
  getPublisherInfoDetailApi,
  getPublisherInfo7DetailApi,
  focusPublisherApi,
  unfocusPublisherApi,
  queryFocusStatusApi,
  getAllGenreApi,
  getAllCountryApi,
  getAllDeviceApi
} from '@/api/store-information/publisher-detail/index';

  import { SearchOutlined } from '@ant-design/icons-vue';

  // 新增：发行商详情数据
  
  const publisherInfo = ref<any>({});
  const route = useRoute();
  const gameDetail = ref<any>({});
  let trendChart: any = null;
  const fetchPublisherDetail = async () => {
  try {
    // 优先使用传递的publisherName，如果没有则使用id（兼容旧版本）
    const publisherName = (route.query.name || route.query.id) as string;
    if (!publisherName) return;
    const res = await getPublisherGameInfoDetailApi(publisherName);
    console.log('fetchPublisherDetail API返回数据:', res);
    publisherInfo.value = res || {};
  } catch (e) {
    console.error('fetchPublisherDetail API调用失败:', e);
    publisherInfo.value = {};
  }
  };
  // 新增：七日趋势相关
type RangeValue = [Dayjs, Dayjs];
const trendDateRange = ref<[Dayjs, Dayjs]>([dayjs('2025-04-19'), dayjs('2025-04-19')]);
const trendRangePresets = ref([
  { label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
  { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
  { label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
  { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
  { label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
  { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
  { label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
  { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
]);

const trendCountry = ref<string[]>([]);

const countryOptions = ref<{ value: string; label: string }[]>([]);

const trendPlatform = ref<string[]>([]);
const platformOptions = ref<{ value: string; label: string }[]>([]);
const fetchPlatforms = async () => {
  try {
    const res = await getAllDeviceApi();
    // 格式化平台数据，确保与之前一致
    platformOptions.value = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value === 'apple' ? 'App Store' : 
             item.value === 'google' ? 'Google Play' : 
             item.value
    }));
  } catch (e) {
    platformOptions.value = [];
  }
};
interface GameType {
  value: string;
  label: string;
}

const trendGenre = ref<string[]>([]);
const genreOptions = ref<GameType[]>([]);
// 获取国家数据
const fetchCountries = async () => {
  try {
    const res = await getAllCountryApi();
    
    if (res && Array.isArray(res)) {
      // 过滤掉无效的国家数据
      countryOptions.value = res
        .filter((item: any) => {
          return item && 
                 typeof item === 'object' &&
                 item.value &&
                 typeof item.value === 'string' &&
                 item.value.trim() !== '' &&
                 item.value !== '5' &&
                 item.value !== '6'; // 过滤掉数字ID
        })
        .map((item: any) => ({
          value: item.value.trim(),
          label: item.value.trim(),
        }));
    } else {
      throw new Error('API返回数据格式不正确');
    }
  } catch (e) {
    // API失败时使用默认国家列表
    countryOptions.value = [
      { value: '菲律宾', label: '菲律宾' },
      { value: '柬埔寨', label: '柬埔寨' },
      { value: '马来西亚', label: '马来西亚' },
      { value: '泰国', label: '泰国' },
      { value: '文莱', label: '文莱' },
      { value: '新加坡', label: '新加坡' },
      { value: '印度尼西亚', label: '印度尼西亚' },
      { value: '越南', label: '越南' },
      { value: '缅甸', label: '缅甸' },
      { value: '中国台湾', label: '中国台湾' },
      { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
    ];
    console.error('获取国家数据失败:', e);
  }
};
const fetchGameTypes = async () => {
  try {
    const response = await getAllGenreApi();
    if (response && Array.isArray(response)) {
      genreOptions.value = [
        ...response.map(item => ({
          value: item.value || '',
          label: item.label || item.value || ''
        }))
      ];
    }
  } catch (error) {
    console.error('获取游戏类型失败:', error);
  }
};

const games = ref<{ value: string; label: string }[]>([]);
      const fetchGameGenres = async () => {
        try {
          const res = await getAllGenreApi();
          games.value = (res || []).map((item: any) => ({
            value: item.value,
            label: item.value,
          }));
        } catch (e) {
          games.value = [];
        }
      };
const trendType = ref<'收入' | '下载量'>('收入');
const trendTypeOptions = [
  { value: '收入', label: '收入' },
  { value: '下载量', label: '下载量' },
];

const trendChartData = ref<{ date: string; value: number }[]>([]);

const hasSearched = ref(false);
const loading = ref(false);
const trendChartRef = ref<HTMLDivElement | null>(null);

const trendIncome = ref<string[]>([]);
const trendIncomeOptions = [
  { value: 'free', label: '免费' },
  { value: 'paid', label: '付费' },
];

const hasData = computed(() => {
  return trendChartData.value.length > 0 && trendChartData.value.some(item => item.value > 0);
});

const initTrendChart = () => {
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value);
    trendChart.setOption(getTrendChartOption());
  }
};

const updateTrendChart = () => {
  if (trendChart) {
    trendChart.setOption(getTrendChartOption());
    trendChart.resize();
  }
};

const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { style: { color: '#666' } }, `+${omittedValues.length}...`);
};

const selectAllCountries = () => {
  trendCountry.value = countryOptions.map(item => item.value);
};

const selectAllPlatforms = () => {
  trendPlatform.value = platformOptions.map(item => item.value);
};

const selectAllGenres = () => {
  trendGenre.value = genreOptions.value.map(item => item.value);
};

const selectAllIncomes = () => {
  trendIncome.value = trendIncomeOptions.map(item => item.value);
};

const handleCountryChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllCountries();
  }
};

const handlePlatformChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllPlatforms();
  }
};

const handleGenreChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllGenres();
  }
};

const handleIncomeChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllIncomes();
  }
};

const handleSearch = async () => {
  try {
    loading.value = true;
    hasSearched.value = true;
    let startTime = '';
    let endTime = '';
    if (trendDateRange.value && trendDateRange.value.length === 2) {
      startTime = trendDateRange.value[0].format('YYYY-MM-DD');
      endTime = trendDateRange.value[1].format('YYYY-MM-DD');
    }
    const response = await getPublisherInfo7DetailApi({
      countryName: trendCountry.value,
      deviceName: trendPlatform.value,
      gameCategory: trendGenre.value,
      isPaid: trendIncome.value,
      startTime: startTime,
      endTime: endTime,
      publisherId: route.query.id as string,
    });
    
    // 更新图表数据
    if (response && Array.isArray(response)) {
      trendChartData.value = response.map(item => {
        const date = Object.keys(item)[0];
        const value = parseInt(item[date]) || 0;
        return {
          date: date,
          value: value
        };
      });
      
      // 确保数据更新后再更新图表
      await nextTick();
      updateTrendChart();
    } else {
      trendChartData.value = [];
      updateTrendChart();
    }
  } catch (error) {
    console.error('获取趋势数据失败:', error);
    trendChartData.value = [];
    updateTrendChart();
  } finally {
    loading.value = false;
  }
};

const getTrendChartOption = () => {
  const dates = trendChartData.value.map(item => item.date);
  const values = trendChartData.value.map(item => item.value);
  const hasData = trendChartData.value.length > 0 && values.some(v => v > 0);

  const option: any = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45,
        formatter: (value: string) => value
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'bar',
        data: values,
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  };
  return option;
};


  const fetchGameDetail = async () => {
  try {
    // 优先使用传递的publisherName，如果没有则使用id（兼容旧版本）
    const publisherName = (route.query.name || route.query.id) as string;
    console.log('传递的publisherName:', publisherName);
    const res = await getPublisherInfoDetailApi(publisherName);
    console.log('fetchGameDetail API返回数据:', res);
    gameDetail.value = res || {};
  } catch (e) {
    console.error('fetchGameDetail API调用失败:', e);
    gameDetail.value = {};
  }
};
 onMounted(() => {
  console.log('页面加载，路由参数:', route.query);
  fetchGameGenres();
  fetchPublisherDetail();
  fetchGameDetail();
  fetchGameTypes();
  fetchCountries();
  fetchPlatforms();
  handleSearch()

  // 图表初始化可根据 gameDetail.value.gameList 数据动态渲染
  // myChart1 = echarts.init(document.getElementById('details'));
  // 示例静态数据，实际可用 gameDetail.value.gameList 处理
  // myChart1.setOption({
  //   xAxis: {
  //     type: 'category',
  //     data: (gameDetail.value.gameList || []).map(item => item.appName) || [],
  //   },
  //   yAxis: { type: 'value' },
  //   series: [
  //     {
  //       data: (gameDetail.value.gameList || []).map(item => Number(item.totalDownloads) || 0),
  //       type: 'bar',
  //       barWidth: '40%',
  //     },
  //   ],
  // });
    nextTick(() => {
    initTrendChart();
    window.addEventListener('resize', () => {
      if (trendChart) {
        trendChart.resize();
      }
    });
  });
});
  onUnmounted(() => {
    // myChart1?.dispose();

  if (trendChart) {
    trendChart.dispose();
    trendChart = null;
  }
  window.removeEventListener('resize', () => {
    if (trendChart) {
      trendChart.resize();
    }
  });
  });
  
  const options = ref([
    {
      value: 'china',
      label: '中国',
    },
    {
      value: 'jpn',
      label: '日本',
    },
  ]);
  const handleChange = (value) => {
    console.log(value); // { key: "lucy", label: "Lucy (101)" }
  };
  const value = ref({
    value: 'nation',
    label: '请选择国家/地区',
  });
  
  const SHOW_PARENT = TreeSelect.SHOW_PARENT;
  const treeData: TreeSelectProps['treeData'] = [
    {
      label: '手机端',
      value: 'mc',
      children: [
        {
          label: 'IPhone',
          value: 'iPhone',
          children: [
            {
              label: 'iPhone 15',
              value: 'iPhone 15',
            },
            {
              label: 'iPhone 15 Pro',
              value: 'iPhone 15 pro',
            },
          ],
        },
        {
          label: '华为',
          value: 'huawei',
          children: [
            {
              label: 'HUAWEI Mate 70',
              value: 'HUAWEI Mate 70',
            },
            {
              label: 'HUAWEI Mate X6',
              value: 'HUAWEI Mate X6',
            },
          ],
        },
      ],
    },
    {
      label: '电脑端',
      value: 'pc',
  
      children: [
        {
          label: 'Child Node3',
          value: '0-1-0',
        },
        {
          label: 'Child Node4',
          value: '0-1-1',
        },
        {
          label: 'Child Node5',
          value: '0-1-2',
        },
      ],
    },
  ];
  
  watch(value, () => {
    console.log(value.value);
  });
  
  const options2 = ref([
    {
      value: 'jingji',
      label: '竞技',
    },
    {
      value: 'Android',
      label: '安卓',
    },
  ]);
  const value2 = ref({
    value: 'shebei',
    label: '请选择游戏类别',
  });
  
  const options3 = ref([
    {
      value: 'downloads',
      label: '下载量',
    },
    {
      value: 'income',
      label: '净收入',
    },
  ]);
  const value3 = ref({
    value: 'download',
    label: '下载量',
  });
  
  const schemas: FormSchema[] = [
    {
      label: '日期选择',
      field: 'dateSelect',
      component: 'DatePicker',
      componentProps: {
        //日期格式化，页面上显示的值
        format: 'YYYY-MM-DD',
        //返回值格式化（绑定值的格式）
        valueFormat: 'YYYY-MM-DD',
        //是否显示今天按钮
        showToday: true,
        //不可选择日期
        disabledDate: (currentDate) => {
          let date = dayjs(currentDate).format('YYYY-MM-DD');
          let nowDate = dayjs(new Date()).format('YYYY-MM-DD');
          //当天不可选择
          if (date == nowDate) {
            return true;
          }
          return false;
        },
      },
    },
  ];
  
  //定义表格列字段
  const columns1: BasicColumn[] = [
    {
      title: '游戏名称',
      dataIndex: 'gamename',
      key: 'gamename',
    },
    {
      title: '上新时间',
      dataIndex: 'uptime',
      key: 'uptime',
    },
    {
      title: '上月下载量',
      dataIndex: 'downloads',
      key: 'downloads',
    },
    {
      title: '上月收入',
      dataIndex: 'income',
      key: 'income',
    },
  ];
  const columns2: BasicColumn[] = [
    {
      title: '游戏名称',
      dataIndex: 'gamename',
      key: 'gamename',
    },
    {
      title: '更新时间',
      dataIndex: 'uptime',
      key: 'uptime',
    },
    {
      title: '上月下载量',
      dataIndex: 'downloads',
      key: 'downloads',
    },
    {
      title: '上月收入',
      dataIndex: 'income',
      key: 'income',
    },
  ];
  const columns3: BasicColumn[] = [
    {
      title: '游戏名称',
      dataIndex: 'gamename',
      key: 'gamename',
    },
    {
      title: '国家',
      dataIndex: 'nation',
      key: 'nation',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
    },
    {
      title: '最新更新日期',
      dataIndex: 'uptime',
      key: 'uptime',
    },
    {
      title: '发布日期',
      dataIndex: 'postdate',
      key: 'postdate',
    },
    {
      title: '上月下载量',
      dataIndex: 'downloads',
      key: 'downloads',
    },
    {
      title: '上月收入',
      dataIndex: 'income',
      key: 'income',
    },
  ];
  
  /** useListPage 是整个框架的核心用于表格渲染，里边封装了很多公共方法；
   * 平台通过此封装，简化了代码，支持自定义扩展*/
  // 通过hook useListPage渲染表格（设置dataSource、columns、actionColumn等参数）
  const { tableContext: tableContext1 } = useListPage({
    designScope: 'table1',
    tableProps: {
      title: '上新游戏',
      dataSource: [
        {
          key: '1',
          gamename: '银河帝国',
          uptime: '2024/6/7',
          downloads: '319w',
          income: '21w',
        },
        {
          key: '2',
          gamename: '银河游侠',
          uptime: '2024/9/5',
          downloads: '663w',
          income: '34w',
        },
        {
          key: '3',
          gamename: '幻影街',
          uptime: '2024/2/6',
          downloads: '927w',
          income: '997w',
        },
        {
          key: '4',
          gamename: '银河边境',
          uptime: '2024/9/5',
          downloads: '762w',
          income: '13w',
        },
      ],
      columns: columns1,
      size: 'small',
      actionColumn: {
        width: 120,
      },
      showActionColumn: false,
      striped: true,
      showTableSetting: false,
      pagination: false,
    },
  });
  // BasicTable绑定注册
  const [registerTable1] = tableContext1;
  
  const { tableContext: tableContext2 } = useListPage({
    designScope: 'table2',
    tableProps: {
      title: '更新游戏',
      dataSource: [
        {
          key: '1',
          gamename: '银河帝国',
          uptime: '2024/6/7',
          downloads: '319w',
          income: '21w',
        },
        {
          key: '2',
          gamename: '银河游侠',
          uptime: '2024/9/5',
          downloads: '663w',
          income: '34w',
        },
        {
          key: '3',
          gamename: '幻影街',
          uptime: '2024/2/6',
          downloads: '927w',
          income: '997w',
        },
        {
          key: '4',
          gamename: '银河边境',
          uptime: '2024/9/5',
          downloads: '762w',
          income: '13w',
        },
      ],
      columns: columns2,
      size: 'small',
      actionColumn: {
        width: 120,
      },
      showActionColumn: false,
      striped: true,
      showTableSetting: false,
      pagination: false,
    },
  });
  // BasicTable绑定注册
  const [registerTable2] = tableContext2;
  
  const { tableContext: tableContext3 } = useListPage({
    designScope: 'table2',
    tableProps: {
      title: '更新游戏',
      dataSource: [
        {
          key: '1',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
        {
          key: '2',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
        {
          key: '3',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
        {
          key: '4',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
        {
          key: '5',
          gamename: '异星遗迹',
          nation: '中国',
          price: '免费',
          uptime: '2024/5/9',
          postdate: '2024/4/25',
          downloads: '798w',
          income: '8000w',
        },
      ],
      columns: columns3,
      size: 'small',
      actionColumn: {
        width: 120,
      },
      showActionColumn: false,
      striped: true,
      showTableSetting: false,
      pagination: false,
    },
  });
  // BasicTable绑定注册
  const [registerTable3] = tableContext3;
  /**
   * 操作栏
   */
  // function getTableAction(record): ActionItem[] {
  //   return [
  //     {
  //       label: '编辑',
  //       onClick: handleEdit.bind(null, record),
  //     },
  //   ];
  // }
  
  // function handleEdit(record) {
  //   console.log(record);
  // }

const focusStatus = ref(false); // false: 未关注，true: 已关注

const fetchFocusStatus = async () => {
  try {
    // 优先使用传递的publisherName，如果没有则使用id（兼容旧版本）
    const publisherName = (route.query.name || route.query.id) as string;
    if (!publisherName) return;
    const res = await queryFocusStatusApi(publisherName);
    // result为"已关注"或"未关注"
    focusStatus.value = res === '已关注';
  } catch {
    focusStatus.value = false;
  }
};

const handleFocusClick = async () => {
  // 优先使用传递的publisherName，如果没有则使用id（兼容旧版本）
  const publisherName = (route.query.name || route.query.id) as string;
  if (!publisherName) return;
  if (focusStatus.value) {
    // 已关注，点击取关
    try {
      await unfocusPublisherApi(publisherName);
      focusStatus.value = false;
      message.success('已取消关注');
    } catch {
      message.error('取消关注失败');
    }
  } else {
    // 未关注，点击关注
    try {
      await focusPublisherApi(publisherName);
      focusStatus.value = true;
      message.success('关注成功');
    } catch {
      message.error('关注失败');
    }
  }
};

onMounted(() => {
  fetchFocusStatus();
});
  </script>
  <style scoped>
/* 页面容器 */
.publisher-detail-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 发行商信息卡片 */
.publisher-info-card {
  background: linear-gradient(135deg, #0ba2e3 0%, #667eea 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  color: white;
  position: relative;
  overflow: hidden;
}

.publisher-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.publisher-header {
  display: flex;
  align-items: center;
  gap: 24px;
  position: relative;
  z-index: 1;
}

.publisher-avatar {
  flex-shrink: 0;
}

.publisher-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.publisher-basic-info {
  flex: 1;
}

.publisher-name {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.publisher-company {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.publisher-actions {
  flex-shrink: 0;
}

.focus-btn {
  height: 44px;
  padding: 0 24px;
  border-radius: 22px;
  font-weight: 600;
  font-size: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.focus-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.focus-btn.focused {
  background: rgba(76, 175, 80, 0.9);
  border-color: #4caf50;
}

.publisher-intro {
  margin-top: 24px;
  display: flex;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.intro-label {
  flex-shrink: 0;
  font-size: 18px;
  font-weight: 600;
  writing-mode: vertical-lr;
  text-orientation: mixed;
  padding: 8px 0;
  border-left: 3px solid rgba(255, 255, 255, 0.6);
  padding-left: 12px;
}

.intro-content {
  flex: 1;
  font-size: 16px;
  line-height: 1.6;
  opacity: 0.95;
}

.intro-content p {
  margin: 0;
  text-indent: 2em;
}

/* 统计数据卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0ba2e3, #667eea);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.games-icon {
  background: linear-gradient(135deg, #0ba2e3, #667eea);
}

.downloads-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.revenue-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
}

.stat-value.primary {
  color: #667eea;
}

.stat-value.warning {
  color: #f5576c;
}

.stat-value.danger {
  color: #00f2fe;
}

/* 游戏列表卡片 */
.games-table-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  padding: 24px 32px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1a1a1a;
}

.title-icon {
  font-size: 24px;
}

.game-count {
  background: #f0f9ff;
  color: #0369a1;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
}

.table-container {
  padding: 0 32px 32px;
}

.modern-table :deep(.ant-table-thead > tr > th) {
  background: #fafbfc;
  border-bottom: 2px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  padding: 16px;
}

.modern-table :deep(.ant-table-tbody > tr > td) {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.modern-table :deep(.ant-table-tbody > tr:hover > td) {
  background: #f8fafc;
}

.game-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.game-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  object-fit: cover;
}

.game-name {
  font-weight: 500;
  color: #1f2937;
}

.number-cell {
  font-weight: 600;
  color: #059669;
}

/* 趋势图表卡片 */
.trend-chart-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.filter-section {
  padding: 24px 32px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.search-btn {
  height: 40px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #0ba2e3, #667eea);
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.search-btn:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.chart-wrapper {
  padding: 32px;
}

.trend-chart {
  width: 100%;
  height: 400px;
  border-radius: 8px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-tip {
  font-size: 14px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .publisher-detail-container {
    padding: 16px;
  }

  .publisher-info-card {
    padding: 24px;
  }

  .publisher-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .publisher-name {
    font-size: 24px;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .card-header {
    padding: 16px 20px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-container {
    padding: 0 20px 20px;
  }

  .filter-section {
    padding: 16px 20px;
  }

  .chart-wrapper {
    padding: 20px;
  }

  .trend-chart {
    height: 300px;
  }
}

/* Ant Design 组件样式覆盖 */
:deep(.ant-select-selector) {
  border-radius: 8px !important;
  border: 1px solid #d1d5db !important;
}

:deep(.ant-select-selector:hover) {
  border-color: #667eea !important;
}

:deep(.ant-select-focused .ant-select-selector) {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

:deep(.ant-picker) {
  border-radius: 8px !important;
  border: 1px solid #d1d5db !important;
}

:deep(.ant-picker:hover) {
  border-color: #667eea !important;
}

:deep(.ant-picker-focused) {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

:deep(.ant-table-pagination) {
  margin-top: 24px;
  padding: 0 16px;
}
  </style>